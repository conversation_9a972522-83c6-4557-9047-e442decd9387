#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赛马娘数据提取工具 - 独立版本
用于解压和解析.br格式的游戏数据文件
"""

import brotli
import json
import os
import sys
import gzip
import zlib
import time

def try_decompress_with_brotli(file_path):
    """尝试使用brotli库解压"""
    try:
        import brotli
        print("✅ 找到brotli库，使用brotli解压...")
        
        with open(file_path, 'rb') as f:
            compressed_data = f.read()
        
        decompressed_data = brotli.decompress(compressed_data)
        return decompressed_data
        
    except ImportError:
        print("❌ 未找到brotli库")
        return None
    except Exception as e:
        print(f"❌ brotli解压失败: {e}")
        return None

def try_decompress_with_gzip(file_path):
    """尝试使用gzip解压"""
    try:
        print("🔄 尝试使用gzip解压...")
        
        with gzip.open(file_path, 'rb') as f:
            decompressed_data = f.read()
        
        return decompressed_data
        
    except Exception as e:
        print(f"❌ gzip解压失败: {e}")
        return None

def try_decompress_with_zlib(file_path):
    """尝试使用zlib解压"""
    try:
        print("🔄 尝试使用zlib解压...")
        
        with open(file_path, 'rb') as f:
            compressed_data = f.read()
        
        decompressed_data = zlib.decompress(compressed_data)
        return decompressed_data
        
    except Exception as e:
        print(f"❌ zlib解压失败: {e}")
        return None

def analyze_raw_data(file_path):
    """分析原始文件数据"""
    try:
        print("🔍 分析原始文件数据...")
        
        with open(file_path, 'rb') as f:
            data = f.read()
        
        print(f"文件大小: {len(data)} 字节")
        print(f"文件头部 (前20字节): {data[:20]}")
        print(f"文件头部 (十六进制): {data[:20].hex()}")
        
        # 检查是否包含可读文本
        try:
            text_sample = data[:1000].decode('utf-8', errors='ignore')
            if len(text_sample.strip()) > 0:
                print("🔍 文件可能包含文本数据:")
                print(text_sample[:200] + "..." if len(text_sample) > 200 else text_sample)
        except:
            pass
        
        # 检查是否是JSON格式
        try:
            json_data = json.loads(data.decode('utf-8'))
            print("✅ 文件是未压缩的JSON格式！")
            return data
        except:
            pass
        
        return None
        
    except Exception as e:
        print(f"❌ 分析原始数据失败: {e}")
        return None

def save_decompressed_data(data, output_path):
    """保存解压后的数据"""
    try:
        # 确保输出路径是文件而不是目录
        if os.path.isdir(output_path):
            print(f"❌ 输出路径是目录，不是文件: {output_path}")
            return False
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"📁 创建输出目录: {output_dir}")
        
        # 尝试解析为JSON
        try:
            json_data = json.loads(data.decode('utf-8'))
            
            # 确保输出文件有.json扩展名
            if not output_path.lower().endswith('.json'):
                output_path = output_path + '.json'
            
            # 保存为格式化的JSON
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 成功保存JSON文件: {output_path}")
            
            # 分析JSON结构
            analyze_json_structure(json_data)
            
            return True
            
        except json.JSONDecodeError:
            # 保存为原始文本
            if output_path.lower().endswith('.json'):
                output_path = output_path.replace('.json', '.txt')
            elif not output_path.lower().endswith('.txt'):
                output_path = output_path + '.txt'
                
            with open(output_path, 'wb') as f:
                f.write(data)
            
            print(f"✅ 成功保存文本文件: {output_path}")
            return True
            
    except PermissionError as e:
        print(f"❌ 权限错误: {e}")
        print("💡 建议: 请检查文件路径权限或以管理员身份运行")
        return False
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        print(f"💡 输出路径: {output_path}")
        return False

def analyze_json_structure(json_data):
    """分析JSON数据结构"""
    print("\n📊 JSON数据结构分析:")
    
    if isinstance(json_data, dict):
        print(f"数据类型: 字典")
        print(f"顶级键数量: {len(json_data)}")
        print(f"顶级键: {list(json_data.keys())[:10]}")  # 只显示前10个
        
        # 查找事件相关的键
        event_keys = []
        for key in json_data.keys():
            key_str = str(key).lower()
            if any(keyword in key_str for keyword in ['event', 'story', 'scenario', 'choice']):
                event_keys.append(key)
        
        if event_keys:
            print(f"🎭 可能的事件相关键: {event_keys}")
            
            # 分析第一个事件键的内容
            first_event_key = event_keys[0]
            first_event_data = json_data[first_event_key]
            
            if isinstance(first_event_data, list) and len(first_event_data) > 0:
                print(f"\n🔍 分析 '{first_event_key}' (包含 {len(first_event_data)} 个项目):")
                
                first_item = first_event_data[0]
                if isinstance(first_item, dict):
                    print(f"  项目字段: {list(first_item.keys())}")
                    
                    # 显示第一个项目的部分内容
                    print(f"  第一个项目示例:")
                    for key, value in list(first_item.items())[:5]:
                        if isinstance(value, str) and len(value) > 50:
                            value = value[:50] + "..."
                        print(f"    {key}: {value}")
    
    elif isinstance(json_data, list):
        print(f"数据类型: 列表")
        print(f"项目数量: {len(json_data)}")
        
        if len(json_data) > 0:
            first_item = json_data[0]
            if isinstance(first_item, dict):
                print(f"项目字段: {list(first_item.keys())}")

def get_default_output_path(input_path):
    """根据输入文件生成默认输出路径"""
    base_name = os.path.splitext(os.path.basename(input_path))[0]
    return f"{base_name}_extracted.json"

def get_file_type_hint(file_path):
    """根据文件名推测文件类型"""
    file_name = os.path.basename(file_path).lower()
    
    if 'event' in file_name:
        return "事件数据"
    elif 'support' in file_name or 'card' in file_name:
        return "支援卡数据"
    elif 'character' in file_name or 'uma' in file_name or 'chara' in file_name:
        return "角色数据"
    elif 'skill' in file_name:
        return "技能数据"
    elif 'race' in file_name:
        return "比赛数据"
    elif 'name' in file_name:
        return "名称数据"
    else:
        return "未知数据类型"

def extract_br_file(input_path, output_path):
    """提取单个br文件"""
    print(f"\n🔄 开始解压: {os.path.basename(input_path)}")
    
    # 尝试多种解压方法
    decompressed_data = None
    
    # 方法1: 使用brotli
    decompressed_data = try_decompress_with_brotli(input_path)
    
    # 方法2: 使用gzip
    if decompressed_data is None:
        decompressed_data = try_decompress_with_gzip(input_path)
    
    # 方法3: 使用zlib
    if decompressed_data is None:
        decompressed_data = try_decompress_with_zlib(input_path)
    
    # 方法4: 分析原始数据
    if decompressed_data is None:
        decompressed_data = analyze_raw_data(input_path)
    
    if decompressed_data is None:
        print("❌ 所有解压方法都失败了")
        return False
    
    # 保存解压后的数据
    if save_decompressed_data(decompressed_data, output_path):
        print(f"✅ 解压成功: {output_path}")
        return True
    else:
        print(f"❌ 保存失败: {output_path}")
        return False

def main():
    print("🏇 赛马娘数据提取工具 v2.0")
    print("=" * 50)
    
    # 检查命令行参数
    if len(sys.argv) >= 2:
        # 命令行模式
        input_path = sys.argv[1]
        output_path = sys.argv[2] if len(sys.argv) >= 3 else get_default_output_path(input_path)
        
        if not os.path.exists(input_path):
            print(f"❌ 文件不存在: {input_path}")
            return
        
        print(f"📁 命令行模式")
        print(f"  输入: {input_path}")
        print(f"  输出: {output_path}")
        print(f"  文件类型: {get_file_type_hint(input_path)}")
        
        extract_br_file(input_path, output_path)
    else:
        # 交互模式
        print("请输入要解压的.br文件路径:")
        input_path = input("文件路径: ").strip().strip('"')
        
        if not os.path.exists(input_path):
            print(f"❌ 文件不存在: {input_path}")
            return
        
        # 显示文件信息
        file_size = os.path.getsize(input_path)
        file_type = get_file_type_hint(input_path)
        print(f"\n📁 文件信息:")
        print(f"  路径: {input_path}")
        print(f"  大小: {file_size:,} 字节")
        print(f"  推测类型: {file_type}")
        
        # 获取输出路径
        default_output = get_default_output_path(input_path)
        print(f"\n💾 输出设置:")
        print(f"  默认输出: {default_output}")
        
        custom_output = input("自定义输出路径 (回车使用默认): ").strip().strip('"')
        output_path = custom_output if custom_output else default_output
        
        print(f"\n🔄 开始处理...")
        extract_br_file(input_path, output_path)
    
    print("\n✨ 处理完成！")

if __name__ == "__main__":
    main()
