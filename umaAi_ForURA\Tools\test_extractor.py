#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据提取工具测试脚本
用于验证工具是否正常工作
"""

import os
import sys
import subprocess
import json

def test_environment():
    """测试运行环境"""
    print("🔧 测试运行环境...")
    
    # 测试Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("需要Python 3.8+")
        return False
    else:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 测试brotli库
    try:
        import brotli
        print("✅ brotli库已安装")
    except ImportError:
        print("❌ brotli库未安装")
        print("正在尝试安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "brotli"])
            print("✅ brotli库安装成功")
        except subprocess.CalledProcessError:
            print("❌ brotli库安装失败")
            return False
    
    return True

def test_file_exists():
    """测试必要文件是否存在"""
    print("\n📁 检查必要文件...")
    
    required_files = [
        "simple_br_extractor.py",
        "extract_br.bat",
        "../events/events.br"
    ]
    
    all_exist = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            all_exist = False
    
    return all_exist

def test_extraction():
    """测试数据提取功能"""
    print("\n🔄 测试数据提取功能...")
    
    test_file = "../events/events.br"
    output_file = "test_output.json"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    try:
        # 运行提取工具
        result = subprocess.run([
            sys.executable, "simple_br_extractor.py", 
            test_file, output_file
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 提取工具运行成功")
        else:
            print(f"❌ 提取工具运行失败: {result.stderr}")
            return False
        
        # 检查输出文件
        if os.path.exists(output_file):
            print(f"✅ 输出文件已生成: {output_file}")
            
            # 检查文件大小
            file_size = os.path.getsize(output_file)
            print(f"📊 文件大小: {file_size:,} 字节")
            
            # 尝试解析JSON
            try:
                with open(output_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list):
                    print(f"✅ JSON格式正确，包含 {len(data)} 个项目")
                    
                    # 显示第一个项目的结构
                    if len(data) > 0:
                        first_item = data[0]
                        if isinstance(first_item, dict):
                            print(f"📋 数据结构: {list(first_item.keys())}")
                        
                elif isinstance(data, dict):
                    print(f"✅ JSON格式正确，字典类型，包含 {len(data)} 个键")
                    print(f"📋 顶级键: {list(data.keys())[:5]}")
                
                # 清理测试文件
                os.remove(output_file)
                print("🧹 测试文件已清理")
                
                return True
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                return False
                
        else:
            print("❌ 输出文件未生成")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 提取工具运行超时")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False

def test_batch_script():
    """测试批处理脚本"""
    print("\n🔧 测试批处理脚本...")
    
    if not os.path.exists("extract_br.bat"):
        print("❌ extract_br.bat 不存在")
        return False
    
    print("✅ extract_br.bat 存在")
    print("💡 建议手动测试批处理脚本:")
    print("   1. 双击 extract_br.bat")
    print("   2. 选择交互模式")
    print("   3. 测试单文件解压功能")
    
    return True

def generate_test_report():
    """生成测试报告"""
    print("\n📊 生成测试报告...")
    
    report = {
        "test_time": "2025-01-22",
        "environment": {
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "platform": sys.platform
        },
        "tests": {
            "environment": test_environment(),
            "files": test_file_exists(),
            "extraction": test_extraction(),
            "batch_script": test_batch_script()
        }
    }
    
    # 保存报告
    with open("test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("✅ 测试报告已保存: test_report.json")
    
    # 显示总结
    passed_tests = sum(1 for result in report["tests"].values() if result)
    total_tests = len(report["tests"])
    
    print(f"\n🎯 测试总结: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！工具可以正常使用")
        return True
    else:
        print("⚠️  部分测试失败，请检查上述错误信息")
        return False

def main():
    print("🏇 赛马娘数据提取工具 - 测试脚本")
    print("=" * 50)
    
    success = generate_test_report()
    
    if success:
        print("\n🚀 工具测试完成，可以开始使用！")
        print("\n📖 使用方法:")
        print("1. 双击 extract_br.bat 启动交互模式")
        print("2. 或使用命令行: python simple_br_extractor.py file.br")
        print("3. 查看 数据提取工具使用说明.md 获取详细帮助")
    else:
        print("\n❌ 工具测试失败，请解决上述问题后重试")
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    main()
