# UmamusumeDeserializeDB5 项目分析文档

## 📋 项目概述

**项目名称**: UmamusumeDeserializeDB5  
**项目地址**: https://github.com/UmamusumeResponseAnalyzer/UmamusumeDeserializeDB5  
**项目作用**: 赛马娘游戏数据生成工具，专门用于从游戏数据库和网络数据生成事件效果数据  
**开发语言**: C# (.NET)  
**输出格式**: .br文件（Brotli压缩的JSON数据）

## 🎯 项目核心功能

### **主要作用**
这个项目是**我们events.br文件的数据源**！它负责：
1. **从游戏数据库提取原始事件数据**
2. **从网络攻略站点获取事件效果描述**
3. **将文本描述转换为数值化的SuccessEffectValue**
4. **生成最终的events.br文件**

### **数据流程**
```
游戏数据库(master.mdb) + 网络攻略数据 → 文本解析 → 数值化 → events.br
```

## 🔧 核心技术实现

### **1. 事件效果文本解析 (Program.cs)**

这是项目的**核心功能**，将日文效果描述转换为数值数组：

```csharp
// EffectValue.Values数组的10个位置含义：
public int[] Values = new int[10];  
// [0] 速度 (スピード)
// [1] 耐力 (スタミナ) 
// [2] 力量 (パワー)
// [3] 根性 (根性)
// [4] 智慧 (賢さ)
// [5] 技能点 (スキルPt)
// [6] 技能提示 (ヒント)
// [7] 体力 (体力)
// [8] 羁绊 (絆)
// [9] 干劲 (やる気)
```

### **2. 文本解析规则**

**解析过程**：
```csharp
public static EffectValue? ParseEffectValue(string effect)
{
    // 1. 按"、"分割效果文本
    foreach (string s in effect.Split("、"))
    {
        // 2. 识别效果类型（速度、体力等）
        int effectId = EffectTextToId.FindIndex(x => t.Contains(x));
        
        // 3. 提取数值（正负数）
        Match m = Regex.Match(t, "[+-]\\d+");
        
        // 4. 映射到Values数组对应位置
        if (effectId < 10) {
            ret.Values[effectId] = effectValue;
        }
    }
}
```

**示例解析**：
```
输入: "体力+30、やる気↑、全ステータス+10"
输出: Values = [10, 10, 10, 10, 10, 0, 0, 30, 0, 1]
解释: 全属性+10, 体力+30, 干劲+1
```

### **3. 特殊效果处理**

**全属性效果**：
```csharp
case 10: // 全ステータス
case 14: // 全能力  
case 16: // 全パフォーマンス
    for (int i = 0; i < 5; ++i)
        ret.Values[i] = effectValue;  // 前5位都设置相同值
```

**随机效果**：
```csharp
case 12: // ランダムな
case 15: // ランダムで
    // 随机效果平均分配到5个属性
    for (int i = 0; i < 5; ++i)
        ret.Values[i] += effectValue / 5;
```

**特殊干劲处理**：
```csharp
if (t.Contains("やる気↑"))     // 干劲上升
    ret.Values[9] = 1;
```

## 📊 数据来源分析

### **1. 游戏数据库 (master.mdb)**
```csharp
// SQLite数据库路径
string UmamusumeDatabaseFilePath = 
    "LocalLow/Cygames/umamusume/master/master.mdb";

// 主要表格：
// - single_mode_story_data: 事件基础信息
// - text_data: 事件名称和文本
// - support_card_data: 支援卡信息
```

### **2. 网络攻略数据 (kamigame.jp)**
```csharp
// 从kamigame网站获取事件效果数据
var kamigame = JArray.Parse(new WebClient().DownloadString(
    "https://kamigame.jp/vls-kamigame-gametool/json/..."));

// 数据结构：
// [0] 事件名称
// [1] 事件类别  
// [2] 触发角色
// [4] 选择项描述
// [5] 成功效果文本 ← 这里是关键！
// [6] 失败效果文本
```

## 🔍 TriggerName生成逻辑

### **剧本事件识别**
```csharp
// Events.cs 第317-331行
if (j.gallery_main_scenario != 0)
{
    triggerName = j.gallery_main_scenario switch
    {
        1 => "URA",           // URA剧本
        2 => "青春杯",         // 青春杯剧本
        3 => "偶像杯",         // 偶像杯剧本
        4 => "巅峰杯",         // 巅峰杯剧本
        5 => "女神杯",         // 女神杯剧本
        6 => "LArc",          // LArc剧本
        7 => "UAF",           // UAF剧本
        8 => "田园杯",         // 田园杯剧本
        9 => "机甲杯",         // 机甲杯剧本
        10 => "传奇杯",        // 传奇杯剧本
        11 => "海岛杯",        // 海岛杯剧本
        _ => "未知剧本"
    };
}
```

### **支援卡事件识别**
```csharp
// 支援卡事件的TriggerName格式：
// "[剧情标题]角色名" 或 "角色名"

// 例如：
// "[輝く景色の、その先に]サイレンススズカ"
// 表示：沉默铃鹿的专属剧情事件
```

## 🛠️ 项目结构分析

### **核心文件**
```
UmamusumeDeserializeDB5/
├── Program.cs              # 主程序和文本解析核心
├── Generator/
│   ├── Events.cs          # 事件数据生成器（核心）
│   ├── SuccessEvent.cs    # 成功事件特殊处理
│   └── [其他生成器...]
└── output/                # 输出目录（生成.br文件）
```

### **Events.cs 核心逻辑**
1. **数据获取** (第17-105行): 从数据库读取基础事件数据
2. **网络数据获取** (第141行): 从kamigame获取效果描述
3. **文本纠正** (第144-169行): 处理数据不一致问题
4. **效果解析** (第235-236行): 调用ParseEffectValue转换文本
5. **数据匹配** (第259-275行): 将网络数据与数据库数据匹配
6. **输出生成** (第284行): 保存为events.br文件

## 💡 对我们项目的意义

### **数据准确性**
- ✅ **官方数据源** - 直接从游戏数据库提取
- ✅ **社区验证** - 结合kamigame等攻略站点数据
- ✅ **持续更新** - 可以获取最新的游戏版本数据

### **SuccessEffectValue的真实含义**
现在我们终于明白了！**SuccessEffectValue不是游戏原始数据，而是这个工具生成的！**

```json
"SuccessEffectValue": {
    "Values": [10, 0, 0, 0, 0, 20, 0, 30, 0, 1]
}
```

**解读**：
- `Values[0] = 10` → 速度+10
- `Values[5] = 20` → 技能点+20  
- `Values[7] = 30` → 体力+30
- `Values[9] = 1`  → 干劲+1

### **为什么有些FailedEffectValue是null**
```csharp
public static EffectValue? ParseEffectValue(string effect)
{
    if (effect.Length == 0) return null;    // 空效果返回null
}
```

当失败效果为空字符串时，返回null，这就是我们看到的null值！

## 🚀 如何使用这个项目

### **环境要求**
- .NET 6.0+
- Visual Studio 2022
- 赛马娘游戏客户端（提供master.mdb数据库）

### **运行步骤**
1. **安装游戏** - 确保有master.mdb文件
2. **修改路径** - 更新Program.cs中的数据库路径
3. **编译运行** - 生成最新的events.br文件
4. **获取输出** - 在output目录找到生成的.br文件

### **自定义修改**
- **添加新效果类型** - 修改EffectTextToId列表
- **调整解析规则** - 修改ParseEffectValue方法
- **更新数据源** - 修改kamigame URL或添加新数据源

## 📈 项目价值评估

### **对我们的帮助**
1. **理解数据结构** - 完全明白了events.br的生成过程
2. **验证数据准确性** - 可以对比我们提取的数据
3. **获取最新数据** - 可以生成最新版本的事件数据
4. **学习解析技术** - 学习专业的文本解析方法

### **可能的改进方向**
1. **集成到我们的项目** - 直接使用其解析逻辑
2. **扩展支持** - 添加对其他数据类型的支持
3. **自动化更新** - 定期获取最新数据
4. **本地化处理** - 添加中文翻译功能

## 🎯 结论

**UmamusumeDeserializeDB5是我们events.br文件的"制造工厂"！**

它解决了我们一直困惑的问题：
- ✅ **SuccessEffectValue的含义** - 现在完全清楚了
- ✅ **数据的来源** - 游戏数据库+网络攻略数据
- ✅ **生成过程** - 文本解析→数值化→压缩输出
- ✅ **更新机制** - 可以获取最新游戏数据

这个发现对我们的URA剧本AI项目具有**重大意义**，我们现在可以：
1. **准确理解事件数据** - 知道每个数值的确切含义
2. **实现精确的事件系统** - 基于真实的数据结构
3. **保持数据同步** - 获取最新的游戏版本数据
4. **改进AI决策** - 基于准确的事件效果进行决策

---

**文档创建时间**: 2025年1月22日  
**分析基于版本**: UmamusumeDeserializeDB5-master  
**重要发现**: events.br是由此工具生成，不是游戏原始数据！
