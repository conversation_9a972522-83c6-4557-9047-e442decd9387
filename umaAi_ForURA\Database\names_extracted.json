{"$type": "System.Collections.Generic.List`1[[UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer]], System.Private.CoreLib", "$values": [{"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 101, "Name": "駿川たづな", "Nickname": "绿帽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 102, "Name": "秋川理事長", "Nickname": "理事"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 103, "Name": "乙名史記者", "Nickname": "记者"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 104, "Name": "桐生院葵", "Nickname": "桐生"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 106, "Name": "樫本理子", "Nickname": "理子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 108, "Name": "ライトハロー", "Nickname": "B95"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 111, "Name": "都留岐涼花", "Nickname": "凉花"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1001, "Name": "スペシャルウィーク", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1002, "Name": "サイレンススズカ", "Nickname": "铃鹿"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1003, "Name": "トウカイテイオー", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1004, "Name": "マルゼンスキー", "Nickname": "司机"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1005, "Name": "フジキセキ", "Nickname": "富士"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1006, "Name": "オグリキャップ", "Nickname": "小栗"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1007, "Name": "ゴールドシップ", "Nickname": "金船"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1008, "Name": "ウオッカ", "Nickname": "伏特"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1009, "Name": "ダイワスカーレット", "Nickname": "大和"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1010, "Name": "タイキシャトル", "Nickname": "大树"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1011, "Name": "グラスワンダー", "Nickname": "草上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1012, "Name": "ヒシアマゾン", "Nickname": "亚马"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1013, "Name": "メジロマックイーン", "Nickname": "麦昆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1014, "Name": "エルコンドルパサー", "Nickname": "神鹰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1015, "Name": "テイエムオペラオー", "Nickname": "好歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1016, "Name": "ナリタブライアン", "Nickname": "白仁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1017, "Name": "シンボリルドルフ", "Nickname": "皇帝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1018, "Name": "エアグルーヴ", "Nickname": "气槽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1019, "Name": "アグネスデジタル", "Nickname": "数码"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1020, "Name": "セイウンスカイ", "Nickname": "青云"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1021, "Name": "タマモクロス", "Nickname": "玉藻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1022, "Name": "ファインモーション", "Nickname": "美妙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1023, "Name": "ビワハヤヒデ", "Nickname": "大头"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1024, "Name": "マヤノトップガン", "Nickname": "重炮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1025, "Name": "マンハッタンカフェ", "Nickname": "茶座"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1026, "Name": "ミホノブルボン", "Nickname": "波旁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1027, "Name": "メジロライアン", "Nickname": "赖恩"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1028, "Name": "ヒシアケボノ", "Nickname": "菱曙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1029, "Name": "ユキノビジン", "Nickname": "雪美"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1030, "Name": "ライスシャワー", "Nickname": "米浴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1031, "Name": "アイネスフウジン", "Nickname": "风神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1032, "Name": "アグネスタキオン", "Nickname": "速子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1033, "Name": "アドマイヤベガ", "Nickname": "织姬"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1034, "Name": "イナリワン", "Nickname": "稻荷"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1035, "Name": "ウイニングチケット", "Nickname": "奖券"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1036, "Name": "エアシャカール", "Nickname": "神宫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1037, "Name": "エイシンフラッシュ", "Nickname": "闪耀"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1038, "Name": "カレンチャン", "Nickname": "卡莲"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1039, "Name": "カワカミプリンセス", "Nickname": "川上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1040, "Name": "ゴールドシチー", "Nickname": "金城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1041, "Name": "サクラバクシンオー", "Nickname": "进王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1042, "Name": "シーキングザパール", "Nickname": "采珠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1043, "Name": "シンコウウインディ", "Nickname": "新光"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1044, "Name": "スイープトウショウ", "Nickname": "东商"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1045, "Name": "スーパークリーク", "Nickname": "溪流"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1046, "Name": "スマートファルコン", "Nickname": "寄子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1047, "Name": "ゼンノロブロイ", "Nickname": "荒漠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1048, "Name": "トーセンジョーダン", "Nickname": "佐敦"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1049, "Name": "ナカヤマフェスタ", "Nickname": "庆典"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1050, "Name": "ナリタタイシン", "Nickname": "大进"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1051, "Name": "ニシノフラワー", "Nickname": "西野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1052, "Name": "ハルウララ", "Nickname": "春丽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1053, "Name": "バンブーメモリー", "Nickname": "青竹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1054, "Name": "ビコーペガサス", "Nickname": "微光"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1055, "Name": "マーベラスサンデー", "Nickname": "周日"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1056, "Name": "マチカネフクキタル", "Nickname": "福来"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1057, "Name": "ミスターシービー", "Nickname": "ＣＢ"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1058, "Name": "メイショウドトウ", "Nickname": "怒涛"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1059, "Name": "メジロドーベル", "Nickname": "多伯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1060, "Name": "ナイスネイチャ", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1061, "Name": "キングヘイロー", "Nickname": "圣王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1062, "Name": "マチカネタンホイザ", "Nickname": "诗歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1063, "Name": "イクノディクタス", "Nickname": "生野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1064, "Name": "メジロパーマー", "Nickname": "善信"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1065, "Name": "ダイタクヘリオス", "Nickname": "太阳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1066, "Name": "ツインターボ", "Nickname": "涡轮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1067, "Name": "サトノダイヤモンド", "Nickname": "光钻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1068, "Name": "キタサンブラック", "Nickname": "北黑"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1069, "Name": "サクラチヨノオー", "Nickname": "千代"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1070, "Name": "シリウスシンボリ", "Nickname": "天狼"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1071, "Name": "メジロアルダン", "Nickname": "尔丹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1072, "Name": "ヤエノムテキ", "Nickname": "八重"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1073, "Name": "ツルマルツヨシ", "Nickname": "鹤丸"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1074, "Name": "メジロブライト", "Nickname": "光<PERSON>"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1075, "Name": "デアリングタクト", "Nickname": "谋勇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1076, "Name": "サクラローレル", "Nickname": "桂冠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1077, "Name": "ナリタトップロード", "Nickname": "成田"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1078, "Name": "ヤマニンゼファー", "Nickname": "也文"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1079, "Name": "フリオーソ", "Nickname": "狂怒"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1080, "Name": "トランセンド", "Nickname": "创升"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1081, "Name": "エスポワールシチー", "Nickname": "希望"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1082, "Name": "ノースフライト", "Nickname": "北飞"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1083, "Name": "シンボリクリスエス", "Nickname": "吉兆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1084, "Name": "タニノギムレット", "Nickname": "谷野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1085, "Name": "ダイイチルビー", "Nickname": "红宝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1086, "Name": "メジロラモーヌ", "Nickname": "高峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1087, "Name": "アストンマーチャン", "Nickname": "真弓"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1088, "Name": "サトノクラウン", "Nickname": "皇冠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1089, "Name": "シュヴァルグラン", "Nickname": "高尚"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1090, "Name": "ヴィルシーナ", "Nickname": "极峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1091, "Name": "ヴィブロス", "Nickname": "强击"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1092, "Name": "ダンツフレーム", "Nickname": "烈焰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1093, "Name": "ケイエスミラクル", "Nickname": "凯斯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1094, "Name": "ジャングルポケット", "Nickname": "宝穴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1095, "Name": "ビリーヴ", "Nickname": "信念"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1096, "Name": "ノーリーズン", "Nickname": "莫名"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1097, "Name": "スティルインラブ", "Nickname": "往昔"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1098, "Name": "コパノリッキー", "Nickname": "小林"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1099, "Name": "ホッコータルマエ", "Nickname": "北港"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1100, "Name": "ワンダーアキュート", "Nickname": "奇锐"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1102, "Name": "サウンズオブアース", "Nickname": "万籁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1103, "Name": "ロイスアンドロイス", "Nickname": "莱斯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1104, "Name": "カツラギエース", "Nickname": "葛城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1105, "Name": "ネオユニヴァース", "Nickname": "新宇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1106, "Name": "ヒシミラクル", "Nickname": "奇宝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1107, "Name": "タップダンスシチー", "Nickname": "舞城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1108, "Name": "ドゥラメンテ", "Nickname": "大锤"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1109, "Name": "ラインクラフト", "Nickname": "莱茵"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1110, "Name": "シーザリオ", "Nickname": "西沙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1111, "Name": "エアメサイア", "Nickname": "救主"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1112, "Name": "デアリングハート", "Nickname": "勇敢"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1113, "Name": "フサイチパンドラ", "Nickname": "火神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1114, "Name": "ブエナビスタ", "Nickname": "景致"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1115, "Name": "オルフェーヴル", "Nickname": "巨匠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1116, "Name": "ジェンティルドンナ", "Nickname": "贵妇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1117, "Name": "ウインバリアシオン", "Nickname": "芭蕾"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1119, "Name": "ドリームジャーニー", "Nickname": "梦旅"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1120, "Name": "カルストンライトオ", "Nickname": "金镇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1121, "Name": "デュランダル", "Nickname": "多旺"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1124, "Name": "バブルガムフェロー", "Nickname": "吹波"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1127, "Name": "フェノーメノ", "Nickname": "超常"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1128, "Name": "ブラストワンピース", "Nickname": "防爆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1129, "Name": "アーモンドアイ", "Nickname": "杏目"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1130, "Name": "ラッキーライラック", "Nickname": "旺紫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1131, "Name": "グランアレグリア", "Nickname": "放声"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1132, "Name": "ラヴズオンリーユー", "Nickname": "唯爱"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1133, "Name": "クロノジェネシス", "Nickname": "创世"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 1134, "Name": "カレンブーケドール", "Nickname": "金花"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 2001, "Name": "ハッピーミーク", "Nickname": "米可"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 2002, "Name": "ビターグラッセ", "Nickname": "糖衣"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 2003, "Name": "リトルココン", "Nickname": "蚕茧"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 2004, "Name": "モンジュー", "Nickname": "望族"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 2005, "Name": "ヴェニュスパーク", "Nickname": "卓芙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 2006, "Name": "リガントーナ", "Nickname": "里格"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 2007, "Name": "ソノンエルフィー", "Nickname": "索诺"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 2008, "Name": "ST-2", "Nickname": "ST2"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9001, "Name": "駿川たづな", "Nickname": "绿帽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9002, "Name": "秋川理事長", "Nickname": "理事"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9003, "Name": "乙名史悦子", "Nickname": "记者"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9004, "Name": "桐生院葵", "Nickname": "桐生"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9005, "Name": "安心沢刺々美", "Nickname": "庸医"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9006, "Name": "樫本理子", "Nickname": "理子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9007, "Name": "ビューティー安心沢", "Nickname": "庸医"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9008, "Name": "ライトハロー", "Nickname": "B95"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9040, "Name": "ダーレーアラビアン", "Nickname": "女神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9041, "Name": "ゴドルフィンバルブ", "Nickname": "蓝神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9042, "Name": "バイアリーターク", "Nickname": "黄神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9043, "Name": "佐岳メイ", "Nickname": "佐岳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9044, "Name": "都留岐涼花", "Nickname": "凉花"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9045, "Name": "シュガーライツ", "Nickname": "砂糖"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9046, "Name": "セントライト", "Nickname": "蓝登"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9047, "Name": "スピードシンボリ", "Nickname": "老登"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9048, "Name": "ハイセイコー", "Nickname": "红登"}, {"$type": "UmamusumeResponseAnalyzer.Entities.BaseName, UmamusumeResponseAnalyzer", "Id": 9049, "Name": "タッカーブライン", "Nickname": "塔克"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Type": 103, "Id": 10001, "Name": "[トレセン学園]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1002, "Type": 101, "Id": 10002, "Name": "[トレセン学園]", "Nickname": "铃鹿"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1003, "Type": 101, "Id": 10003, "Name": "[トレセン学園]", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1004, "Type": 101, "Id": 10004, "Name": "[トレセン学園]", "Nickname": "司机"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1006, "Type": 102, "Id": 10005, "Name": "[トレセン学園]", "Nickname": "小栗"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1007, "Type": 105, "Id": 10006, "Name": "[トレセン学園]", "Nickname": "金船"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1008, "Type": 102, "Id": 10007, "Name": "[トレセン学園]", "Nickname": "伏特"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1010, "Type": 101, "Id": 10008, "Name": "[トレセン学園]", "Nickname": "大树"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1011, "Type": 103, "Id": 10009, "Name": "[トレセン学園]", "Nickname": "草上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1013, "Type": 105, "Id": 10010, "Name": "[トレセン学園]", "Nickname": "麦昆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1014, "Type": 102, "Id": 10011, "Name": "[トレセン学園]", "Nickname": "神鹰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1015, "Type": 105, "Id": 10012, "Name": "[トレセン学園]", "Nickname": "好歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1017, "Type": 106, "Id": 10013, "Name": "[トレセン学園]", "Nickname": "皇帝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1020, "Type": 105, "Id": 10014, "Name": "[トレセン学園]", "Nickname": "青云"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1030, "Type": 105, "Id": 10015, "Name": "[トレセン学園]", "Nickname": "米浴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1035, "Type": 103, "Id": 10016, "Name": "[トレセン学園]", "Nickname": "奖券"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1040, "Type": 101, "Id": 10017, "Name": "[トレセン学園]", "Nickname": "金城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1041, "Type": 101, "Id": 10018, "Name": "[トレセン学園]", "Nickname": "进王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1045, "Type": 105, "Id": 10019, "Name": "[トレセン学園]", "Nickname": "溪流"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1052, "Type": 103, "Id": 10020, "Name": "[トレセン学園]", "Nickname": "春丽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9001, "Type": 0, "Id": 10021, "Name": "[トレセン学園]", "Nickname": "绿帽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9004, "Type": 0, "Id": 10022, "Name": "[トレセン学園]", "Nickname": "桐生"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1009, "Type": 106, "Id": 10023, "Name": "[トレセン学園]", "Nickname": "大和"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1012, "Type": 102, "Id": 10024, "Name": "[トレセン学園]", "Nickname": "亚马"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1018, "Type": 103, "Id": 10025, "Name": "[トレセン学園]", "Nickname": "气槽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1019, "Type": 102, "Id": 10026, "Name": "[トレセン学園]", "Nickname": "数码"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1021, "Type": 105, "Id": 10027, "Name": "[トレセン学園]", "Nickname": "玉藻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1022, "Type": 106, "Id": 10028, "Name": "[トレセン学園]", "Nickname": "美妙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1023, "Type": 102, "Id": 10029, "Name": "[トレセン学園]", "Nickname": "大头"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1024, "Type": 105, "Id": 10030, "Name": "[トレセン学園]", "Nickname": "重炮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1025, "Type": 105, "Id": 10031, "Name": "[トレセン学園]", "Nickname": "茶座"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1026, "Type": 102, "Id": 10032, "Name": "[トレセン学園]", "Nickname": "波旁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1027, "Type": 102, "Id": 10033, "Name": "[トレセン学園]", "Nickname": "赖恩"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1029, "Type": 103, "Id": 10034, "Name": "[トレセン学園]", "Nickname": "雪美"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1031, "Type": 103, "Id": 10035, "Name": "[トレセン学園]", "Nickname": "风神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1032, "Type": 106, "Id": 10036, "Name": "[トレセン学園]", "Nickname": "速子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1036, "Type": 106, "Id": 10037, "Name": "[トレセン学園]", "Nickname": "神宫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1037, "Type": 101, "Id": 10038, "Name": "[トレセン学園]", "Nickname": "闪耀"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1046, "Type": 102, "Id": 10039, "Name": "[トレセン学園]", "Nickname": "寄子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1050, "Type": 101, "Id": 10040, "Name": "[トレセン学園]", "Nickname": "大进"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1051, "Type": 101, "Id": 10041, "Name": "[トレセン学園]", "Nickname": "西野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1054, "Type": 101, "Id": 10042, "Name": "[トレセン学園]", "Nickname": "微光"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1055, "Type": 106, "Id": 10043, "Name": "[トレセン学園]", "Nickname": "周日"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1056, "Type": 106, "Id": 10044, "Name": "[トレセン学園]", "Nickname": "福来"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1058, "Type": 103, "Id": 10045, "Name": "[トレセン学園]", "Nickname": "怒涛"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1059, "Type": 106, "Id": 10046, "Name": "[トレセン学園]", "Nickname": "多伯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1060, "Type": 103, "Id": 10047, "Name": "[トレセン学園]", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1061, "Type": 101, "Id": 10048, "Name": "[トレセン学園]", "Nickname": "圣王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1005, "Type": 106, "Id": 10049, "Name": "[トレセン学園]", "Nickname": "富士"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1044, "Type": 101, "Id": 10050, "Name": "[トレセン学園]", "Nickname": "东商"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1066, "Type": 101, "Id": 10051, "Name": "[トレセン学園]", "Nickname": "涡轮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1065, "Type": 102, "Id": 10052, "Name": "[トレセン学園]", "Nickname": "太阳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1063, "Type": 106, "Id": 10053, "Name": "[トレセン学園]", "Nickname": "生野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1064, "Type": 103, "Id": 10054, "Name": "[トレセン学園]", "Nickname": "善信"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1068, "Type": 101, "Id": 10055, "Name": "[トレセン学園]", "Nickname": "北黑"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1067, "Type": 105, "Id": 10056, "Name": "[トレセン学園]", "Nickname": "光钻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1062, "Type": 103, "Id": 10057, "Name": "[トレセン学園]", "Nickname": "诗歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1072, "Type": 102, "Id": 10058, "Name": "[トレセン学園]", "Nickname": "八重"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1047, "Type": 105, "Id": 10059, "Name": "[トレセン学園]", "Nickname": "荒漠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9006, "Type": 0, "Id": 10060, "Name": "[トレセン学園]", "Nickname": "理子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1042, "Type": 103, "Id": 10061, "Name": "[トレセン学園]", "Nickname": "采珠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1069, "Type": 105, "Id": 10062, "Name": "[トレセン学園]", "Nickname": "千代"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1039, "Type": 101, "Id": 10063, "Name": "[トレセン学園]", "Nickname": "川上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1028, "Type": 103, "Id": 10064, "Name": "[トレセン学園]", "Nickname": "菱曙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1053, "Type": 102, "Id": 10065, "Name": "[トレセン学園]", "Nickname": "青竹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1043, "Type": 101, "Id": 10066, "Name": "[トレセン学園]", "Nickname": "新光"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1049, "Type": 105, "Id": 10067, "Name": "[トレセン学園]", "Nickname": "庆典"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1034, "Type": 102, "Id": 10068, "Name": "[トレセン学園]", "Nickname": "稻荷"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1071, "Type": 106, "Id": 10069, "Name": "[トレセン学園]", "Nickname": "尔丹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1048, "Type": 105, "Id": 10070, "Name": "[トレセン学園]", "Nickname": "佐敦"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1070, "Type": 103, "Id": 10071, "Name": "[トレセン学園]", "Nickname": "天狼"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1016, "Type": 101, "Id": 10072, "Name": "[トレセン学園]", "Nickname": "白仁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1038, "Type": 106, "Id": 10073, "Name": "[トレセン学園]", "Nickname": "卡莲"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9005, "Type": 0, "Id": 10074, "Name": "[笹針師]", "Nickname": "庸医"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1033, "Type": 102, "Id": 10075, "Name": "[トレセン学園]", "Nickname": "织姬"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1074, "Type": 105, "Id": 10076, "Name": "[トレセン学園]", "Nickname": "光<PERSON>"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1077, "Type": 101, "Id": 10077, "Name": "[トレセン学園]", "Nickname": "成田"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1057, "Type": 106, "Id": 10078, "Name": "[トレセン学園]", "Nickname": "ＣＢ"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1085, "Type": 102, "Id": 10079, "Name": "[トレセン学園]", "Nickname": "红宝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1093, "Type": 103, "Id": 10080, "Name": "[トレセン学園]", "Nickname": "凯斯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1073, "Type": 103, "Id": 10081, "Name": "[トレセン学園]", "Nickname": "鹤丸"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1083, "Type": 105, "Id": 10082, "Name": "[トレセン学園]", "Nickname": "吉兆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9008, "Type": 0, "Id": 10083, "Name": "[イベントプロデューサー]", "Nickname": "B95"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1084, "Type": 102, "Id": 10084, "Name": "[トレセン学園]", "Nickname": "谷野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1076, "Type": 105, "Id": 10085, "Name": "[トレセン学園]", "Nickname": "桂冠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1078, "Type": 103, "Id": 10086, "Name": "[トレセン学園]", "Nickname": "也文"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1087, "Type": 101, "Id": 10087, "Name": "[トレセン学園]", "Nickname": "真弓"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1086, "Type": 106, "Id": 10088, "Name": "[トレセン学園]", "Nickname": "高峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1094, "Type": 101, "Id": 10089, "Name": "[トレセン学園]", "Nickname": "宝穴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1099, "Type": 105, "Id": 10090, "Name": "[トレセン学園]", "Nickname": "北港"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1104, "Type": 101, "Id": 10091, "Name": "[トレセン学園]", "Nickname": "葛城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1098, "Type": 103, "Id": 10092, "Name": "[トレセン学園]", "Nickname": "小林"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1100, "Type": 102, "Id": 10093, "Name": "[トレセン学園]", "Nickname": "奇锐"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9043, "Type": 0, "Id": 10094, "Name": "[URA職員]", "Nickname": "佐岳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1107, "Type": 103, "Id": 10095, "Name": "[トレセン学園]", "Nickname": "舞城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1097, "Type": 101, "Id": 10096, "Name": "[トレセン学園]", "Nickname": "往昔"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1090, "Type": 103, "Id": 10097, "Name": "[トレセン学園]", "Nickname": "极峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1102, "Type": 105, "Id": 10098, "Name": "[トレセン学園]", "Nickname": "万籁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1091, "Type": 103, "Id": 10099, "Name": "[トレセン学園]", "Nickname": "强击"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1103, "Type": 102, "Id": 10100, "Name": "[トレセン学園]", "Nickname": "莱斯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1108, "Type": 101, "Id": 10101, "Name": "[トレセン学園]", "Nickname": "大锤"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1082, "Type": 106, "Id": 10102, "Name": "[トレセン学園]", "Nickname": "北飞"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1115, "Type": 103, "Id": 10103, "Name": "[トレセン学園]", "Nickname": "巨匠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9044, "Type": 0, "Id": 10104, "Name": "[プロデューサー]", "Nickname": "凉花"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1089, "Type": 105, "Id": 10105, "Name": "[トレセン学園]", "Nickname": "高尚"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1105, "Type": 106, "Id": 10106, "Name": "[トレセン学園]", "Nickname": "新宇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1106, "Type": 105, "Id": 10107, "Name": "[トレセン学園]", "Nickname": "奇宝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1092, "Type": 106, "Id": 10108, "Name": "[トレセン学園]", "Nickname": "烈焰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9002, "Type": 0, "Id": 10109, "Name": "[トレセン学園]", "Nickname": "理事"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1081, "Type": 102, "Id": 10110, "Name": "[トレセン学園]", "Nickname": "希望"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1124, "Type": 101, "Id": 10111, "Name": "[トレセン学園]", "Nickname": "吹波"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1116, "Type": 101, "Id": 10113, "Name": "[トレセン学園]", "Nickname": "贵妇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1109, "Type": 103, "Id": 10114, "Name": "[トレセン学園]", "Nickname": "莱茵"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1110, "Type": 106, "Id": 10115, "Name": "[トレセン学園]", "Nickname": "西沙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1128, "Type": 103, "Id": 10116, "Name": "[トレセン学園]", "Nickname": "防爆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1096, "Type": 103, "Id": 10117, "Name": "[トレセン学園]", "Nickname": "莫名"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1114, "Type": 103, "Id": 10118, "Name": "[トレセン学園]", "Nickname": "景致"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1119, "Type": 101, "Id": 10119, "Name": "[トレセン学園]", "Nickname": "梦旅"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1075, "Type": 106, "Id": 10120, "Name": "[トレセン学園]", "Nickname": "谋勇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1112, "Type": 102, "Id": 10121, "Name": "[トレセン学園]", "Nickname": "勇敢"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1129, "Type": 101, "Id": 10122, "Name": "[トレセン学園]", "Nickname": "杏目"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1130, "Type": 106, "Id": 10123, "Name": "[トレセン学園]", "Nickname": "旺紫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1131, "Type": 102, "Id": 10124, "Name": "[トレセン学園]", "Nickname": "放声"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1080, "Type": 103, "Id": 10125, "Name": "[トレセン学園]", "Nickname": "创升"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1134, "Type": 105, "Id": 10126, "Name": "[トレセン学園]", "Nickname": "金花"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1111, "Type": 106, "Id": 10127, "Name": "[トレセン学園]", "Nickname": "救主"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9049, "Type": 0, "Id": 10128, "Name": "[無人島PJ責任者]", "Nickname": "塔克"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1005, "Type": 106, "Id": 20001, "Name": "[やれやれ、お帰り]", "Nickname": "富士"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1009, "Type": 106, "Id": 20002, "Name": "[努力は裏切らない！]", "Nickname": "大和"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1012, "Type": 102, "Id": 20003, "Name": "[テッペンに立て！]", "Nickname": "亚马"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1018, "Type": 103, "Id": 20004, "Name": "[副会長の一刺し]", "Nickname": "气槽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1019, "Type": 102, "Id": 20005, "Name": "[デジタル充電中+]", "Nickname": "数码"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1023, "Type": 102, "Id": 20006, "Name": "[検証、開始]", "Nickname": "大头"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1024, "Type": 105, "Id": 20007, "Name": "[カワイイ＋カワイイは～？]", "Nickname": "重炮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1025, "Type": 105, "Id": 20008, "Name": "[雨の独奏、私の独創]", "Nickname": "茶座"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1026, "Type": 102, "Id": 20009, "Name": "[鍛えぬくトモ]", "Nickname": "波旁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1027, "Type": 102, "Id": 20010, "Name": "[鍛えて、応えて！]", "Nickname": "赖恩"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1029, "Type": 103, "Id": 20011, "Name": "[シチーガール入門♯]", "Nickname": "雪美"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1032, "Type": 106, "Id": 20012, "Name": "[生体Aに関する実験的研究]", "Nickname": "速子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1037, "Type": 101, "Id": 20013, "Name": "[0500・定刻通り]", "Nickname": "闪耀"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1050, "Type": 101, "Id": 20014, "Name": "[波立つキモチ]", "Nickname": "大进"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1055, "Type": 106, "Id": 20015, "Name": "[マーベラス☆大作戦]", "Nickname": "周日"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1056, "Type": 106, "Id": 20016, "Name": "[運の行方]", "Nickname": "福来"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1058, "Type": 103, "Id": 20017, "Name": "[幸せと背中合わせ]", "Nickname": "怒涛"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1059, "Type": 106, "Id": 20018, "Name": "[目線は気にせず]", "Nickname": "多伯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1060, "Type": 103, "Id": 20019, "Name": "[…ただの水滴ですって]", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1061, "Type": 101, "Id": 20020, "Name": "[一流プランニング]", "Nickname": "圣王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9004, "Type": 0, "Id": 20021, "Name": "[共に同じ道を！]", "Nickname": "桐生"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1021, "Type": 103, "Id": 20022, "Name": "[これがウチらのいか焼きや！]", "Nickname": "玉藻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1044, "Type": 101, "Id": 20023, "Name": "[見習い魔女と長い夜]", "Nickname": "东商"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1065, "Type": 102, "Id": 20024, "Name": "[パリピ・ぱーりないと！]", "Nickname": "太阳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1063, "Type": 106, "Id": 20025, "Name": "[準備運動は怠るべからず]", "Nickname": "生野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1060, "Type": 106, "Id": 20026, "Name": "[むじゃむじゃむじゃき]", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1051, "Type": 102, "Id": 20027, "Name": "[あなたにささげる]", "Nickname": "西野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1047, "Type": 105, "Id": 20028, "Name": "[おすすめ本、あります！]", "Nickname": "荒漠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1042, "Type": 103, "Id": 20029, "Name": "[世界の真珠、その名は]", "Nickname": "采珠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1031, "Type": 106, "Id": 20030, "Name": "[頑張るキミに！]", "Nickname": "风神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1043, "Type": 101, "Id": 20031, "Name": "[///WARNING GATE///]", "Nickname": "新光"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1034, "Type": 102, "Id": 20032, "Name": "[泥濘一笑、真っつぐに]", "Nickname": "稻荷"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1014, "Type": 101, "Id": 20033, "Name": "[泥まみれのコンパネーロ]", "Nickname": "神鹰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1071, "Type": 106, "Id": 20034, "Name": "[その心に吹きすさぶ]", "Nickname": "尔丹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1048, "Type": 105, "Id": 20035, "Name": "[nail on Tu<PERSON>]", "Nickname": "佐敦"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1064, "Type": 105, "Id": 20036, "Name": "[救いはあるよっ♪]", "Nickname": "善信"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1022, "Type": 102, "Id": 20037, "Name": "[GRMAラーメン♪]", "Nickname": "美妙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1070, "Type": 103, "Id": 20038, "Name": "[一等星は揺らがない]", "Nickname": "天狼"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1008, "Type": 101, "Id": 20039, "Name": "[うるさい監視役]", "Nickname": "伏特"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1027, "Type": 105, "Id": 20040, "Name": "[鼓動が速まるクールダウン]", "Nickname": "赖恩"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1033, "Type": 103, "Id": 20041, "Name": "[一等星を目指して]", "Nickname": "织姬"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1041, "Type": 102, "Id": 20042, "Name": "[響け！模範的あいさつ！]", "Nickname": "进王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Type": 102, "Id": 20043, "Name": "[1日の終わりに]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1038, "Type": 101, "Id": 20044, "Name": "[ほっと♪きゅーとメモリー]", "Nickname": "卡莲"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1046, "Type": 103, "Id": 20045, "Name": "[ギリギリ!?UMA♡DOL]", "Nickname": "寄子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1044, "Type": 106, "Id": 20046, "Name": "[おてんば魔女、修行中。]", "Nickname": "东商"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1003, "Type": 105, "Id": 20047, "Name": "[Step! Smile! Wink!]", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1006, "Type": 103, "Id": 20048, "Name": "[好きなんだ、ご飯が]", "Nickname": "小栗"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1040, "Type": 106, "Id": 20049, "Name": "[金色を目指して]", "Nickname": "金城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1020, "Type": 106, "Id": 20050, "Name": "[ゆるっと、まにまに]", "Nickname": "青云"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1093, "Type": 103, "Id": 20051, "Name": "[The 3rd MIRACLE]", "Nickname": "凯斯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1073, "Type": 103, "Id": 20052, "Name": "[『フレフレ、ツヨシ！』]", "Nickname": "鹤丸"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1077, "Type": 102, "Id": 20053, "Name": "[ストップ、委員長！]", "Nickname": "成田"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1015, "Type": 105, "Id": 20054, "Name": "[偉大なるフォトグラフィーア]", "Nickname": "好歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1087, "Type": 101, "Id": 20055, "Name": "[添付ファイル、温めますか？]", "Nickname": "真弓"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1094, "Type": 101, "Id": 20056, "Name": "[デッド・エンド【通せん坊】]", "Nickname": "宝穴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1059, "Type": 103, "Id": 20057, "Name": "[ふわり、さらり]", "Nickname": "多伯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1017, "Type": 105, "Id": 20058, "Name": "[放課後、君が笑った]", "Nickname": "皇帝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1032, "Type": 102, "Id": 20059, "Name": "[嗚呼素晴らしき甘味哉]", "Nickname": "速子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1028, "Type": 102, "Id": 20060, "Name": "[甘～い浮き輪を召し上がれ]", "Nickname": "菱曙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1007, "Type": 105, "Id": 20061, "Name": "[ゴルシ印のスペシャリテ]", "Nickname": "金船"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1004, "Type": 106, "Id": 20062, "Name": "[激マブ！ドライブ♪]", "Nickname": "司机"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1002, "Type": 101, "Id": 20063, "Name": "[夕立の向こうからのぞく]", "Nickname": "铃鹿"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1098, "Type": 103, "Id": 20064, "Name": "[ラッキーの神様は早起き]", "Nickname": "小林"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1011, "Type": 102, "Id": 20065, "Name": "[よい茶の飲み置き]", "Nickname": "草上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1068, "Type": 105, "Id": 20066, "Name": "[お助け大将と小さな魔女]", "Nickname": "北黑"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1010, "Type": 106, "Id": 20067, "Name": "[あまえんぼNight]", "Nickname": "大树"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1060, "Type": 105, "Id": 20068, "Name": "[とびっきりの金メダル]", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1062, "Type": 101, "Id": 20069, "Name": "[あくもー退散！？]", "Nickname": "诗歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1090, "Type": 103, "Id": 20070, "Name": "[長姉は大変？]", "Nickname": "极峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1103, "Type": 102, "Id": 20071, "Name": "[『最強』は作れる！]", "Nickname": "莱斯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1084, "Type": 106, "Id": 20072, "Name": "[ギャラルホルンを磨き上げよ]", "Nickname": "谷野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1048, "Type": 101, "Id": 20073, "Name": "[Time flies]", "Nickname": "佐敦"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1072, "Type": 105, "Id": 20074, "Name": "[護るべきは無垢の瞳]", "Nickname": "八重"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1107, "Type": 103, "Id": 20075, "Name": "[Cheers!]", "Nickname": "舞城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1036, "Type": 102, "Id": 20076, "Name": "[道端フィードバック]", "Nickname": "神宫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1092, "Type": 106, "Id": 20077, "Name": "[ぬりぬりシェイプアップ！]", "Nickname": "烈焰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1056, "Type": 101, "Id": 20078, "Name": "[お守りパワーで福徳円満！]", "Nickname": "福来"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1055, "Type": 103, "Id": 20079, "Name": "[みんなにグッドマーベラス☆]", "Nickname": "周日"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1076, "Type": 106, "Id": 20080, "Name": "[君の花衣]", "Nickname": "桂冠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1082, "Type": 102, "Id": 20081, "Name": "[お任せ！オートクチュール]", "Nickname": "北飞"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1124, "Type": 101, "Id": 20082, "Name": "[先陣スプラッシュ！]", "Nickname": "吹波"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1086, "Type": 105, "Id": 20083, "Name": "[月下麗人]", "Nickname": "高峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1130, "Type": 106, "Id": 20084, "Name": "[喜びの咲く刻]", "Nickname": "旺紫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1013, "Type": 103, "Id": 20085, "Name": "[メロンなささやき]", "Nickname": "麦昆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1030, "Type": 101, "Id": 20086, "Name": "[『しあわせの青いバラ』]", "Nickname": "米浴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1112, "Type": 102, "Id": 20087, "Name": "[Daring Music]", "Nickname": "勇敢"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1134, "Type": 105, "Id": 20088, "Name": "[一輪ずつ、喜びを束ねて]", "Nickname": "金花"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1039, "Type": 103, "Id": 20089, "Name": "[私たちのプリンセス流儀]", "Nickname": "川上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Type": 103, "Id": 30001, "Name": "[日本一のステージを]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1002, "Type": 101, "Id": 30002, "Name": "[輝く景色の、その先に]", "Nickname": "铃鹿"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1003, "Type": 101, "Id": 30003, "Name": "[夢は掲げるものなのだっ！]", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1007, "Type": 105, "Id": 30004, "Name": "[不沈艦の進撃]", "Nickname": "金船"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1008, "Type": 102, "Id": 30005, "Name": "[ロード・オブ・ウオッカ]", "Nickname": "伏特"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1011, "Type": 103, "Id": 30006, "Name": "[千紫万紅にまぎれぬ一凛]", "Nickname": "草上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1014, "Type": 102, "Id": 30007, "Name": "[パッションチャンピオーナ！]", "Nickname": "神鹰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1020, "Type": 105, "Id": 30008, "Name": "[待望の大謀]", "Nickname": "青云"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1021, "Type": 105, "Id": 30009, "Name": "[天をも切り裂くイナズマ娘！]", "Nickname": "玉藻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1022, "Type": 106, "Id": 30010, "Name": "[感謝は指先まで込めて]", "Nickname": "美妙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1031, "Type": 103, "Id": 30011, "Name": "[飛び出せ、キラメケ]", "Nickname": "风神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1035, "Type": 103, "Id": 30012, "Name": "[B・N・Winner!!]", "Nickname": "奖券"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1036, "Type": 106, "Id": 30013, "Name": "[7センチの先へ]", "Nickname": "神宫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1040, "Type": 101, "Id": 30014, "Name": "[Run(my)way]", "Nickname": "金城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1041, "Type": 101, "Id": 30015, "Name": "[はやい！うまい！はやい！]", "Nickname": "进王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1045, "Type": 105, "Id": 30016, "Name": "[一粒の安らぎ]", "Nickname": "溪流"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1046, "Type": 102, "Id": 30017, "Name": "[これが私のウマドル道☆]", "Nickname": "寄子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1051, "Type": 101, "Id": 30018, "Name": "[まだ小さな蕾でも]", "Nickname": "西野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1052, "Type": 103, "Id": 30019, "Name": "[うらら～な休日]", "Nickname": "春丽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1054, "Type": 101, "Id": 30020, "Name": "[必殺！Wキャロットパンチ！]", "Nickname": "微光"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9001, "Type": 0, "Id": 30021, "Name": "[ようこそ、トレセン学園へ！]", "Nickname": "绿帽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1013, "Type": 105, "Id": 30022, "Name": "[『エース』として]", "Nickname": "麦昆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1030, "Type": 105, "Id": 30023, "Name": "[『幸せ』が舞う時]", "Nickname": "米浴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1006, "Type": 102, "Id": 30024, "Name": "[『愛してもらうんだぞ』]", "Nickname": "小栗"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Type": 101, "Id": 30025, "Name": "[夕焼けはあこがれの色]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1066, "Type": 101, "Id": 30026, "Name": "[ターボエンジン全開宣言！]", "Nickname": "涡轮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1064, "Type": 103, "Id": 30027, "Name": "[バカと笑え]", "Nickname": "善信"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1068, "Type": 101, "Id": 30028, "Name": "[迫る熱に押されて]", "Nickname": "北黑"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1067, "Type": 105, "Id": 30029, "Name": "[その背中を越えて]", "Nickname": "光钻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1062, "Type": 103, "Id": 30030, "Name": "[Just keep going.]", "Nickname": "诗歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1029, "Type": 106, "Id": 30031, "Name": "[ふるさと直送エール！]", "Nickname": "雪美"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1072, "Type": 102, "Id": 30032, "Name": "[押して忍べど燃ゆるもの]", "Nickname": "八重"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1035, "Type": 102, "Id": 30033, "Name": "[夢はホントに叶うんだ！]", "Nickname": "奖券"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1030, "Type": 102, "Id": 30034, "Name": "[幸せは曲がり角の向こう]", "Nickname": "米浴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9006, "Type": 0, "Id": 30036, "Name": "[徹底管理主義]", "Nickname": "理子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1017, "Type": 103, "Id": 30037, "Name": "[絶対皇帝]", "Nickname": "皇帝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1069, "Type": 105, "Id": 30038, "Name": "[今ぞ盛りのさくら花]", "Nickname": "千代"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1039, "Type": 101, "Id": 30039, "Name": "[花嫁たるもの！！]", "Nickname": "川上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1028, "Type": 103, "Id": 30040, "Name": "[召しませふぁーすとBite！]", "Nickname": "菱曙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1059, "Type": 106, "Id": 30041, "Name": "[おもい、ねがう]", "Nickname": "多伯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1053, "Type": 102, "Id": 30042, "Name": "[Head-on fight！]", "Nickname": "青竹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1049, "Type": 105, "Id": 30043, "Name": "[43、8、1]", "Nickname": "庆典"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1016, "Type": 101, "Id": 30044, "Name": "[Two Pieces]", "Nickname": "白仁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1044, "Type": 101, "Id": 30045, "Name": "[ツメたいヒトリジメ？]", "Nickname": "东商"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1035, "Type": 105, "Id": 30046, "Name": "[全力！タントラムッッ！]", "Nickname": "奖券"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1009, "Type": 102, "Id": 30047, "Name": "[トライフル☆バケーション]", "Nickname": "大和"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1027, "Type": 103, "Id": 30048, "Name": "[爽快！ウイニングショット！]", "Nickname": "赖恩"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9008, "Type": 0, "Id": 30052, "Name": "[from the GROUND UP]", "Nickname": "B95"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1010, "Type": 101, "Id": 30053, "Name": "[Hands up, crook!]", "Nickname": "大树"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1060, "Type": 106, "Id": 30054, "Name": "[願いまでは拭わない]", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1020, "Type": 106, "Id": 30055, "Name": "[明日は全国的に赤でしょう♪]", "Nickname": "青云"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1061, "Type": 102, "Id": 30056, "Name": "[今宵、円舞曲にのせて]", "Nickname": "圣王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1007, "Type": 101, "Id": 30057, "Name": "[ウマ王伝説・最強になった件]", "Nickname": "金船"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1003, "Type": 102, "Id": 30058, "Name": "[ふたつのノーブルライト]", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1026, "Type": 105, "Id": 30059, "Name": "[祝福はフーガ]", "Nickname": "波旁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1066, "Type": 103, "Id": 30060, "Name": "[集まってコンステレーション]", "Nickname": "涡轮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1023, "Type": 106, "Id": 30061, "Name": "[響き合うストレイン]", "Nickname": "大头"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1002, "Type": 105, "Id": 30062, "Name": "[WINNING DREAM]", "Nickname": "铃鹿"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1063, "Type": 103, "Id": 30063, "Name": "[心と足元は温かく]", "Nickname": "生野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1021, "Type": 102, "Id": 30064, "Name": "[やったれハロウィンナイト！]", "Nickname": "玉藻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1047, "Type": 101, "Id": 30065, "Name": "[魔力授かりし英雄]", "Nickname": "荒漠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1026, "Type": 106, "Id": 30066, "Name": "[幽霊さんとハロウィンの魔法]", "Nickname": "波旁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1017, "Type": 0, "Id": 30067, "Name": "[尊尚親愛]", "Nickname": "皇帝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1038, "Type": 106, "Id": 30068, "Name": "[かなし君、うつくし君]", "Nickname": "卡莲"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1016, "Type": 105, "Id": 30069, "Name": "[天嗤う鏑矢]", "Nickname": "白仁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1029, "Type": 103, "Id": 30070, "Name": "[宵照らす奉納舞]", "Nickname": "雪美"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1065, "Type": 102, "Id": 30071, "Name": "[おどれ・さわげ・フェスれ！]", "Nickname": "太阳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1024, "Type": 101, "Id": 30072, "Name": "[フォーメーション：PARTY]", "Nickname": "重炮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1050, "Type": 106, "Id": 30073, "Name": "[いじっぱりのマルクト]", "Nickname": "大进"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1055, "Type": 102, "Id": 30074, "Name": "[スノウクリスタル・デイ]", "Nickname": "周日"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1025, "Type": 105, "Id": 30075, "Name": "[独奏・螺旋追走曲]", "Nickname": "茶座"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1002, "Type": 101, "Id": 30076, "Name": "[まだ見ぬ景色を求めて]", "Nickname": "铃鹿"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1033, "Type": 102, "Id": 30077, "Name": "[夜に暁、空に瑞星]", "Nickname": "织姬"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1056, "Type": 101, "Id": 30078, "Name": "[袖振り合えば福となる♪]", "Nickname": "福来"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1058, "Type": 105, "Id": 30079, "Name": "[飛びも出でぬべき心地すれ]", "Nickname": "怒涛"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9005, "Type": 0, "Id": 30080, "Name": "[ブスッといっとく？]", "Nickname": "庸医"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Type": 0, "Id": 30081, "Name": "[紡がれてゆく想い]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1051, "Type": 106, "Id": 30082, "Name": "[小さなカップに想いをこめて]", "Nickname": "西野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1041, "Type": 103, "Id": 30083, "Name": "[爆速！最速！花あらし！]", "Nickname": "进王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1048, "Type": 101, "Id": 30084, "Name": "[In my way]", "Nickname": "佐敦"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1019, "Type": 102, "Id": 30085, "Name": "[そこに“いる”幸せ]", "Nickname": "数码"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1077, "Type": 101, "Id": 30086, "Name": "[桃色のバックショット]", "Nickname": "成田"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1074, "Type": 105, "Id": 30087, "Name": "[リトル・バイ・リトル]", "Nickname": "光<PERSON>"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1067, "Type": 106, "Id": 30088, "Name": "[SPECIAL DREAMERS!!]", "Nickname": "光钻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1055, "Type": 103, "Id": 30089, "Name": "[世界にもっと☆マーベラス]", "Nickname": "周日"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1017, "Type": 105, "Id": 30090, "Name": "[Enchaînement]", "Nickname": "皇帝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1070, "Type": 106, "Id": 30091, "Name": "[Escorte étoile]", "Nickname": "天狼"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1036, "Type": 101, "Id": 30092, "Name": "[mag!c number]", "Nickname": "神宫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1009, "Type": 102, "Id": 30093, "Name": "[Outfit as No.1]", "Nickname": "大和"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1053, "Type": 103, "Id": 30094, "Name": "[届け、このオモイ！]", "Nickname": "青竹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1042, "Type": 101, "Id": 30095, "Name": "[そのエールは世界を変えた]", "Nickname": "采珠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1039, "Type": 102, "Id": 30096, "Name": "[フレフレ☆プリンセス！]", "Nickname": "川上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1057, "Type": 106, "Id": 30097, "Name": "[Dear Mr. C.<PERSON>.]", "Nickname": "ＣＢ"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1052, "Type": 102, "Id": 30098, "Name": "[うららか・ぱっしょん♪]", "Nickname": "春丽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1063, "Type": 105, "Id": 30099, "Name": "[愛すべき金蘭のつどい]", "Nickname": "生野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1030, "Type": 106, "Id": 30100, "Name": "[あこがれの景色]", "Nickname": "米浴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1032, "Type": 101, "Id": 30101, "Name": "[Q≠0]", "Nickname": "速子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1014, "Type": 103, "Id": 30102, "Name": "[キラキラカケル∞]", "Nickname": "神鹰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1062, "Type": 106, "Id": 30103, "Name": "[マチタン☆アドベンチャー]", "Nickname": "诗歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1047, "Type": 105, "Id": 30104, "Name": "[『ロードナイトと夢の石』]", "Nickname": "荒漠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Type": 103, "Id": 30105, "Name": "[私たちと夢、掴みましょう！]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1018, "Type": 102, "Id": 30106, "Name": "[理想へ向かう青嵐]", "Nickname": "气槽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1004, "Type": 101, "Id": 30107, "Name": "[おセンチ注意報♪]", "Nickname": "司机"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1049, "Type": 106, "Id": 30108, "Name": "[一天地六に身を任せ]", "Nickname": "庆典"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1069, "Type": 103, "Id": 30109, "Name": "[チョベリグ心あれば桜心]", "Nickname": "千代"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1025, "Type": 105, "Id": 30110, "Name": "[It's on the house.]", "Nickname": "茶座"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1003, "Type": 106, "Id": 30111, "Name": "[テイオー・オー・オー！！！]", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1066, "Type": 103, "Id": 30112, "Name": "[TT Ignition!]", "Nickname": "涡轮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1023, "Type": 105, "Id": 30113, "Name": "[ユークロニア・アーキテクト]", "Nickname": "大头"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1085, "Type": 102, "Id": 30114, "Name": "[嗚呼華麗ナル一族]", "Nickname": "红宝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1064, "Type": 105, "Id": 30115, "Name": "[月下の悪魔ちゃん♪]", "Nickname": "善信"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1065, "Type": 101, "Id": 30116, "Name": "[お日さま天使ちゃん♪]", "Nickname": "太阳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1043, "Type": 103, "Id": 30117, "Name": "[とびだせ！恐怖の魔王軍]", "Nickname": "新光"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1083, "Type": 105, "Id": 30118, "Name": "[A <PERSON>esh<PERSON>wed]", "Nickname": "吉兆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1071, "Type": 101, "Id": 30119, "Name": "[夜風に舞うは祈りの花]", "Nickname": "尔丹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1072, "Type": 103, "Id": 30120, "Name": "[天まで焦がせ祈りの火]", "Nickname": "八重"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1026, "Type": 101, "Id": 30121, "Name": "[U & Me]", "Nickname": "波旁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1037, "Type": 101, "Id": 30122, "Name": "[<PERSON><PERSON> s<PERSON>]", "Nickname": "闪耀"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1016, "Type": 102, "Id": 30123, "Name": "[GLAD TIDINGS]", "Nickname": "白仁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1018, "Type": 106, "Id": 30124, "Name": "[Are you merry?]", "Nickname": "气槽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1076, "Type": 105, "Id": 30125, "Name": "[ここからはDon't stop!]", "Nickname": "桂冠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1078, "Type": 103, "Id": 30126, "Name": "[誰も知らない風へ]", "Nickname": "也文"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Type": 105, "Id": 30127, "Name": "[ハネ退け魔を退け願い込め]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1044, "Type": 106, "Id": 30128, "Name": "[気まぐれ絶佳]", "Nickname": "东商"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1011, "Type": 102, "Id": 30129, "Name": "[遥か流るる冬夏青青]", "Nickname": "草上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1093, "Type": 103, "Id": 30130, "Name": "[To you]", "Nickname": "凯斯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1055, "Type": 101, "Id": 30131, "Name": "[マーベラス☆ショコラSHOW]", "Nickname": "周日"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1054, "Type": 102, "Id": 30132, "Name": "[変身！ギャルソンフォーム！]", "Nickname": "微光"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1028, "Type": 106, "Id": 30133, "Name": "[フォンデンテで笑って]", "Nickname": "菱曙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1086, "Type": 106, "Id": 30134, "Name": "[燦爛]", "Nickname": "高峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1104, "Type": 101, "Id": 30135, "Name": "[掲げよ、燃え盛る灯を]", "Nickname": "葛城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1083, "Type": 103, "Id": 30136, "Name": "[Unyielding resolve]", "Nickname": "吉兆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9040, "Type": 0, "Id": 30137, "Name": "[永劫続く栄光へ]", "Nickname": "女神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1060, "Type": 102, "Id": 30138, "Name": "[今は瞳を閉じないで]", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1013, "Type": 105, "Id": 30139, "Name": "[心、夜風にさらわれて]", "Nickname": "麦昆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1003, "Type": 101, "Id": 30140, "Name": "[比翼のワルツ]", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1026, "Type": 106, "Id": 30141, "Name": "[ミッション『心の栄養補給』]", "Nickname": "波旁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1076, "Type": 105, "Id": 30142, "Name": "[刻下、桜花爛漫]", "Nickname": "桂冠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1063, "Type": 102, "Id": 30143, "Name": "[たどり着いた景色]", "Nickname": "生野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1084, "Type": 102, "Id": 30145, "Name": "[Welcome to Umayuru]", "Nickname": "谷野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1006, "Type": 106, "Id": 30146, "Name": "[駆けよ、駆けよ、駆けよ！！]", "Nickname": "小栗"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1094, "Type": 101, "Id": 30147, "Name": "[The frontier]", "Nickname": "宝穴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1009, "Type": 103, "Id": 30148, "Name": "[Something Blue]", "Nickname": "大和"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1087, "Type": 106, "Id": 30149, "Name": "[オートクチュール・メモリー]", "Nickname": "真弓"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1008, "Type": 105, "Id": 30150, "Name": "[ハート・イグニッション！！]", "Nickname": "伏特"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1024, "Type": 102, "Id": 30151, "Name": "[Chill chill night*]", "Nickname": "重炮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1015, "Type": 106, "Id": 30152, "Name": "[All'alba vincerò!]", "Nickname": "好歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1040, "Type": 103, "Id": 30153, "Name": "[優しい月]", "Nickname": "金城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1064, "Type": 101, "Id": 30154, "Name": "[カモメのように]", "Nickname": "善信"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1065, "Type": 106, "Id": 30155, "Name": "[#夏 #新しい自分]", "Nickname": "太阳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1100, "Type": 102, "Id": 30156, "Name": "[ライフロング・ワンダー]", "Nickname": "奇锐"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1025, "Type": 106, "Id": 30157, "Name": "[君と見る泡沫]", "Nickname": "茶座"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1094, "Type": 103, "Id": 30158, "Name": "[トばすぜホットサマー！]", "Nickname": "宝穴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1018, "Type": 101, "Id": 30159, "Name": "[Seaside Bloom]", "Nickname": "气槽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9043, "Type": 0, "Id": 30160, "Name": "[L'aubeは迫りて]", "Nickname": "佐岳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1014, "Type": 101, "Id": 30161, "Name": "[大望は飛んでいく]", "Nickname": "神鹰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1033, "Type": 105, "Id": 30162, "Name": "[Precious☆Moments]", "Nickname": "织姬"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1049, "Type": 106, "Id": 30163, "Name": "[鉄火場に咲く菫]", "Nickname": "庆典"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1012, "Type": 102, "Id": 30164, "Name": "[炎々、闘志を抱き]", "Nickname": "亚马"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1084, "Type": 105, "Id": 30165, "Name": "[フォルトゥーナの喝采]", "Nickname": "谷野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1107, "Type": 103, "Id": 30166, "Name": "[高らかにYO-HO!]", "Nickname": "舞城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1022, "Type": 101, "Id": 30167, "Name": "[Hello my clover]", "Nickname": "美妙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1007, "Type": 101, "Id": 30168, "Name": "[波をかきわけ夢がゆく]", "Nickname": "金船"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1073, "Type": 102, "Id": 30169, "Name": "[天より遣われし微笑み]", "Nickname": "鹤丸"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1061, "Type": 103, "Id": 30170, "Name": "[我が舞、巡りて]", "Nickname": "圣王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1052, "Type": 102, "Id": 30171, "Name": "[ああ、楽しや、楽しや！]", "Nickname": "春丽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1013, "Type": 106, "Id": 30172, "Name": "[かっとばせー！ですわ！？]", "Nickname": "麦昆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1102, "Type": 105, "Id": 30173, "Name": "[大地と我らのアンサンブル]", "Nickname": "万籁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1059, "Type": 101, "Id": 30174, "Name": "[聖夜、変わるために]", "Nickname": "多伯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1086, "Type": 102, "Id": 30175, "Name": "[冬溶かす熾火]", "Nickname": "高峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1027, "Type": 103, "Id": 30176, "Name": "[選んで選んで、想いを込めて]", "Nickname": "赖恩"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1091, "Type": 103, "Id": 30177, "Name": "[会心のウイニングスマイル]", "Nickname": "强击"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1108, "Type": 101, "Id": 30178, "Name": "[血脈の胎動]", "Nickname": "大锤"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1067, "Type": 106, "Id": 30179, "Name": "[百花の願いをこの胸に]", "Nickname": "光钻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1068, "Type": 0, "Id": 30180, "Name": "[この先も！]", "Nickname": "北黑"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1066, "Type": 105, "Id": 30181, "Name": "[ウタエミンナノ]", "Nickname": "涡轮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1099, "Type": 105, "Id": 30182, "Name": "[白い翼は舞い戻りて]", "Nickname": "北港"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1035, "Type": 102, "Id": 30183, "Name": "[小さな笑顔、2つ]", "Nickname": "奖券"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1041, "Type": 101, "Id": 30184, "Name": "[感謝感謝！サクラ吹雪！！]", "Nickname": "进王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1082, "Type": 106, "Id": 30185, "Name": "[ゴー・ファッショニスタ！]", "Nickname": "北飞"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1116, "Type": 101, "Id": 30186, "Name": "[Balliam<PERSON>?]", "Nickname": "贵妇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1115, "Type": 103, "Id": 30187, "Name": "[只、君臨す。]", "Nickname": "巨匠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9044, "Type": 0, "Id": 30188, "Name": "[共に描くキラメキ]", "Nickname": "凉花"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1068, "Type": 103, "Id": 30189, "Name": "[2人のバウンス・シャッセ]", "Nickname": "北黑"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1108, "Type": 105, "Id": 30190, "Name": "[激情のピッツィカ]", "Nickname": "大锤"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1102, "Type": 102, "Id": 30191, "Name": "[ブリッランテな旋律を君と]", "Nickname": "万籁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1091, "Type": 101, "Id": 30192, "Name": "[とっておきのレヴェランス]", "Nickname": "强击"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1089, "Type": 105, "Id": 30193, "Name": "[Over the Ocean]", "Nickname": "高尚"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1109, "Type": 103, "Id": 30194, "Name": "[ティアラが描くレイライン]", "Nickname": "莱茵"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1093, "Type": 102, "Id": 30195, "Name": "[白い鳥のアラベスク]", "Nickname": "凯斯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1078, "Type": 101, "Id": 30196, "Name": "[浚いの風]", "Nickname": "也文"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1053, "Type": 102, "Id": 30197, "Name": "[Burning!!]", "Nickname": "青竹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1105, "Type": 106, "Id": 30198, "Name": "[V.E.R.2285のあなたへ]", "Nickname": "新宇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1106, "Type": 105, "Id": 30199, "Name": "[わーく・あ・みらくる！]", "Nickname": "奇宝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1096, "Type": 103, "Id": 30200, "Name": "[大胆不敵の兵よ]", "Nickname": "莫名"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1050, "Type": 106, "Id": 30201, "Name": "[Take Them Down!]", "Nickname": "大进"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1008, "Type": 103, "Id": 30202, "Name": "[明日のライド・オン]", "Nickname": "伏特"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1094, "Type": 102, "Id": 30203, "Name": "[新時代を開く者]", "Nickname": "宝穴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1020, "Type": 102, "Id": 30204, "Name": "[幸福の匂いにまどろむ]", "Nickname": "青云"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1090, "Type": 101, "Id": 30205, "Name": "[紺碧の戴冠式]", "Nickname": "极峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1091, "Type": 101, "Id": 30206, "Name": "[アルストロメリアの夢]", "Nickname": "强击"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9002, "Type": 0, "Id": 30207, "Name": "[謹製ッ！特大夢にんじん！]", "Nickname": "理事"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1051, "Type": 102, "Id": 30208, "Name": "[朝焼け苺の畑にて]", "Nickname": "西野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1092, "Type": 105, "Id": 30209, "Name": "[そして幕は上がる]", "Nickname": "烈焰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1046, "Type": 101, "Id": 30210, "Name": "[波間のオフショット]", "Nickname": "寄子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1098, "Type": 106, "Id": 30211, "Name": "[Lucky☆Summertime]", "Nickname": "小林"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1100, "Type": 105, "Id": 30212, "Name": "[あの夏のキリトリ線]", "Nickname": "奇锐"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1003, "Type": 101, "Id": 30213, "Name": "[開催☆ハチャメチャGP！]", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1022, "Type": 103, "Id": 30214, "Name": "[歴史も美食も余すところなく]", "Nickname": "美妙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1097, "Type": 101, "Id": 30215, "Name": "[Devilish Whispers]", "Nickname": "往昔"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1114, "Type": 103, "Id": 30216, "Name": "[我が学び舎へ、愛をこめて]", "Nickname": "景致"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1110, "Type": 106, "Id": 30217, "Name": "[慈恵のMother Sea]", "Nickname": "西沙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1010, "Type": 106, "Id": 30218, "Name": "[月下のSunshine]", "Nickname": "大树"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1005, "Type": 103, "Id": 30219, "Name": "[悩み事かい？ポニーちゃん]", "Nickname": "富士"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1083, "Type": 106, "Id": 30220, "Name": "[Rewarding Job]", "Nickname": "吉兆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1067, "Type": 105, "Id": 30221, "Name": "[今日もたのしく、おいしく。]", "Nickname": "光钻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1081, "Type": 102, "Id": 30222, "Name": "[KNOCK 'EM DOWN!]", "Nickname": "希望"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1012, "Type": 103, "Id": 30223, "Name": "[牙を立て、リフレイン]", "Nickname": "亚马"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1016, "Type": 101, "Id": 30224, "Name": "[咆哮のアポヤンド]", "Nickname": "白仁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1056, "Type": 103, "Id": 30225, "Name": "[メークパワートリオ]", "Nickname": "福来"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1036, "Type": 105, "Id": 30226, "Name": "[Cocoon]", "Nickname": "神宫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1009, "Type": 106, "Id": 30227, "Name": "[緋色の君へ風が吹く]", "Nickname": "大和"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1083, "Type": 102, "Id": 30228, "Name": "[COOL⇔CRAZY/Buddy]", "Nickname": "吉兆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1019, "Type": 106, "Id": 30229, "Name": "[【告知】新刊あります！]", "Nickname": "数码"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1058, "Type": 101, "Id": 30230, "Name": "[不屈の遠吠え]", "Nickname": "怒涛"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1015, "Type": 102, "Id": 30231, "Name": "[王を統べて覇す者]", "Nickname": "好歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1128, "Type": 103, "Id": 30232, "Name": "[Blast Off!]", "Nickname": "防爆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1017, "Type": 106, "Id": 30233, "Name": "[雲煙飛動]", "Nickname": "皇帝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1071, "Type": 102, "Id": 30234, "Name": "[深窓の少女へ]", "Nickname": "尔丹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1004, "Type": 101, "Id": 30235, "Name": "[ふりさけみれば天花のわらう]", "Nickname": "司机"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1042, "Type": 101, "Id": 30236, "Name": "[飛び立つヴィーナス]", "Nickname": "采珠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1038, "Type": 103, "Id": 30237, "Name": "[Cutest in Ur ♡]", "Nickname": "卡莲"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1063, "Type": 106, "Id": 30238, "Name": "[鉄の乙女も微笑んで]", "Nickname": "生野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1060, "Type": 105, "Id": 30239, "Name": "[これきり、特別ですよ？]", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1094, "Type": 105, "Id": 30240, "Name": "[『最強は俺だ！！』]", "Nickname": "宝穴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9047, "Type": 0, "Id": 30241, "Name": "[導きの光]", "Nickname": "老登"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1129, "Type": 101, "Id": 30242, "Name": "[世界を変える眼差し]", "Nickname": "杏目"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1131, "Type": 102, "Id": 30243, "Name": "[NEW TALES AWAIT]", "Nickname": "放声"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1074, "Type": 105, "Id": 30244, "Name": "[春、来たれり]", "Nickname": "光<PERSON>"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1008, "Type": 102, "Id": 30245, "Name": "[Ballroom Tempest]", "Nickname": "伏特"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1119, "Type": 101, "Id": 30246, "Name": "[誘うは夢心地]", "Nickname": "梦旅"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1083, "Type": 101, "Id": 30247, "Name": "[Operation: Escort]", "Nickname": "吉兆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1075, "Type": 106, "Id": 30248, "Name": "[無垢の白妙]", "Nickname": "谋勇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1080, "Type": 103, "Id": 30249, "Name": "[繋がれパレード・ノーツ♪]", "Nickname": "创升"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1114, "Type": 102, "Id": 30250, "Name": "[Luz de ensueño]", "Nickname": "景致"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1081, "Type": 103, "Id": 30251, "Name": "[約束のglitter]", "Nickname": "希望"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1002, "Type": 103, "Id": 30252, "Name": "[My Beloved Scenery]", "Nickname": "铃鹿"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1109, "Type": 101, "Id": 30253, "Name": "[Unveiled Dream]", "Nickname": "莱茵"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1112, "Type": 106, "Id": 30254, "Name": "[Blooming Buds]", "Nickname": "勇敢"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1111, "Type": 106, "Id": 30255, "Name": "[いつか深まる若草]", "Nickname": "救主"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1021, "Type": 102, "Id": 30256, "Name": "[白き稲妻の如く]", "Nickname": "玉藻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 9049, "Type": 0, "Id": 30257, "Name": "[本能は吼えているか！？]", "Nickname": "塔克"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1027, "Type": 105, "Id": 30258, "Name": "[瞳に闘志を胸に勝利の渇望を]", "Nickname": "赖恩"}, {"$type": "UmamusumeResponseAnalyzer.Entities.SupportCardName, UmamusumeResponseAnalyzer", "CharaId": 1013, "Type": 101, "Id": 30260, "Name": "[グレイッシュPARTY☆]", "Nickname": "麦昆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Id": 100101, "Name": "[スペシャルドリーマー]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Id": 100102, "Name": "[ほっぴん♪ビタミンハート]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Id": 100103, "Name": "[日之本一の総大将]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1002, "Id": 100201, "Name": "[サイレントイノセンス]", "Nickname": "铃鹿"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1002, "Id": 100202, "Name": "[波間のエメラルド]", "Nickname": "铃鹿"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1003, "Id": 100301, "Name": "[トップ・オブ・ジョイフル]", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1003, "Id": 100302, "Name": "[ビヨンド・ザ・ホライズン]", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1003, "Id": 100303, "Name": "[紫雲の夢見取り]", "Nickname": "帝王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1004, "Id": 100401, "Name": "[フォーミュラオブルージュ]", "Nickname": "司机"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1004, "Id": 100402, "Name": "[ぶっとび☆さまーナイト]", "Nickname": "司机"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1004, "Id": 100403, "Name": "[祝ひ寿ぐ神速天女]", "Nickname": "司机"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1005, "Id": 100501, "Name": "[シューティンスタァ・ルヴュ]", "Nickname": "富士"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1005, "Id": 100502, "Name": "[シュクセ・エトワーレ]", "Nickname": "富士"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1006, "Id": 100601, "Name": "[スターライトビート]", "Nickname": "小栗"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1006, "Id": 100602, "Name": "[キセキの白星]", "Nickname": "小栗"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1007, "Id": 100701, "Name": "[レッドストライフ]", "Nickname": "金船"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1007, "Id": 100702, "Name": "[RUN！乱！ランチャー！！]", "Nickname": "金船"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1007, "Id": 100703, "Name": "[La mode 564]", "Nickname": "金船"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1008, "Id": 100801, "Name": "[ワイルドトップギア]", "Nickname": "伏特"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1008, "Id": 100802, "Name": "[不凍のアクア・ウィタエ]", "Nickname": "伏特"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1009, "Id": 100901, "Name": "[トップ・オブ・ブルー]", "Nickname": "大和"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1009, "Id": 100902, "Name": "[緋色のニュイ・エトワレ]", "Nickname": "大和"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1010, "Id": 101001, "Name": "[ワイルド・フロンティア]", "Nickname": "大树"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1010, "Id": 101002, "Name": "[Bubblegum☆Memories]", "Nickname": "大树"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1011, "Id": 101101, "Name": "[岩穿つ青]", "Nickname": "草上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1011, "Id": 101102, "Name": "[セイントジェード・ヒーラー]", "Nickname": "草上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1011, "Id": 101103, "Name": "[蒼炎の誉]", "Nickname": "草上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1012, "Id": 101201, "Name": "[アマゾネス・ラピス]", "Nickname": "亚马"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1012, "Id": 101202, "Name": "[<PERSON><PERSON>]", "Nickname": "亚马"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1013, "Id": 101301, "Name": "[エレガンス・ライン]", "Nickname": "麦昆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1013, "Id": 101302, "Name": "[エンド・オブ・スカイ]", "Nickname": "麦昆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1013, "Id": 101303, "Name": "[さざ波フェアレディ]", "Nickname": "麦昆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1014, "Id": 101401, "Name": "[エル☆Número 1]", "Nickname": "神鹰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1014, "Id": 101402, "Name": "[ククルカン・モンク]", "Nickname": "神鹰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1015, "Id": 101501, "Name": "[オー・ソレ・スーオ！]", "Nickname": "好歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1015, "Id": 101502, "Name": "[初晴・青き絢爛]", "Nickname": "好歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1016, "Id": 101601, "Name": "[Ma<PERSON><PERSON>]", "Nickname": "白仁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1016, "Id": 101602, "Name": "[餓狼]", "Nickname": "白仁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1017, "Id": 101701, "Name": "[ロード・オブ・エンペラー]", "Nickname": "皇帝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1017, "Id": 101702, "Name": "[皓月の弓取り]", "Nickname": "皇帝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1018, "Id": 101801, "Name": "[エンプレスロード]", "Nickname": "气槽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1018, "Id": 101802, "Name": "[クエルクス・キウィーリス]", "Nickname": "气槽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1019, "Id": 101901, "Name": "[超特急！フルカラー特殊PP]", "Nickname": "数码"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1019, "Id": 101902, "Name": "[愛麗♡キョンシー]", "Nickname": "数码"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1020, "Id": 102001, "Name": "[あおぐもサミング]", "Nickname": "青云"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1020, "Id": 102002, "Name": "[ソワレ・ド・シャトン]", "Nickname": "青云"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1021, "Id": 102101, "Name": "[疾風迅雷]", "Nickname": "玉藻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1021, "Id": 102102, "Name": "[猛ル鳴神]", "Nickname": "玉藻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1022, "Id": 102201, "Name": "[<PERSON>]", "Nickname": "美妙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1022, "Id": 102202, "Name": "[Titania]", "Nickname": "美妙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1023, "Id": 102301, "Name": "[pf.Victory formula...]", "Nickname": "大头"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1023, "Id": 102302, "Name": "[ノエルージュ・キャロル]", "Nickname": "大头"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1023, "Id": 102303, "Name": "[Engineered Victory]", "Nickname": "大头"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1024, "Id": 102401, "Name": "[すくらんぶる☆ゾーン]", "Nickname": "重炮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1024, "Id": 102402, "Name": "[サンライト・ブーケ]", "Nickname": "重炮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1024, "Id": 102403, "Name": "[ろっきん☆MewMeow]", "Nickname": "重炮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1025, "Id": 102501, "Name": "[Creeping <PERSON>]", "Nickname": "茶座"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1025, "Id": 102502, "Name": "[柳緑小夜]", "Nickname": "茶座"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1026, "Id": 102601, "Name": "[MB-19890425]", "Nickname": "波旁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1026, "Id": 102602, "Name": "[CODE：グラサージュ]", "Nickname": "波旁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1027, "Id": 102701, "Name": "[ストレート・ライン]", "Nickname": "赖恩"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1027, "Id": 102702, "Name": "[マーガレット・ラッテ]", "Nickname": "赖恩"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1028, "Id": 102801, "Name": "[ボーノ☆アラモーダ]", "Nickname": "菱曙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1029, "Id": 102901, "Name": "[めんこいめんこいむつのはな]", "Nickname": "雪美"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1029, "Id": 102902, "Name": "[茶の子雪ん子]", "Nickname": "雪美"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1030, "Id": 103001, "Name": "[ローゼスドリーム]", "Nickname": "米浴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1030, "Id": 103002, "Name": "[Make up Vampire!]", "Nickname": "米浴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1030, "Id": 103003, "Name": "[<PERSON><PERSON>]", "Nickname": "米浴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1031, "Id": 103101, "Name": "[オールタイム・フィーバー]", "Nickname": "风神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1031, "Id": 103102, "Name": "[MELTY GIFT]", "Nickname": "风神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1031, "Id": 103103, "Name": "[Hello Hello Island]", "Nickname": "风神"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1032, "Id": 103201, "Name": "[tach-nology]", "Nickname": "速子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1032, "Id": 103202, "Name": "[Lunatic Lab]", "Nickname": "速子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1032, "Id": 103203, "Name": "[Σ Experiment]", "Nickname": "速子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1033, "Id": 103301, "Name": "[<PERSON><PERSON> Nocturne]", "Nickname": "织姬"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1033, "Id": 103302, "Name": "[<PERSON><PERSON><PERSON><PERSON>]", "Nickname": "织姬"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1034, "Id": 103401, "Name": "[稲荷所縁江戸紫]", "Nickname": "稻荷"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1034, "Id": 103402, "Name": "[夢ノ金原]", "Nickname": "稻荷"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1035, "Id": 103501, "Name": "[Go To Winning!]", "Nickname": "奖券"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1035, "Id": 103502, "Name": "[Dream Deliverer]", "Nickname": "奖券"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1035, "Id": 103503, "Name": "[Glorious Coat]", "Nickname": "奖券"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1036, "Id": 103601, "Name": "[unsigned]", "Nickname": "神宫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1036, "Id": 103602, "Name": "[<PERSON><PERSON><PERSON>'s <PERSON>]", "Nickname": "神宫"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1037, "Id": 103701, "Name": "[Meisterschaft]", "Nickname": "闪耀"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1037, "Id": 103702, "Name": "[コレクト・ショコラティエ]", "Nickname": "闪耀"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1037, "Id": 103703, "Name": "[<PERSON><PERSON> Plät<PERSON>n]", "Nickname": "闪耀"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1038, "Id": 103801, "Name": "[フィーユ・エクレール]", "Nickname": "卡莲"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1038, "Id": 103802, "Name": "[朔月のマ・シェリ]", "Nickname": "卡莲"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1039, "Id": 103901, "Name": "[プリンセス・オブ・ピンク]", "Nickname": "川上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1039, "Id": 103902, "Name": "[水干撫子]", "Nickname": "川上"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1040, "Id": 104001, "Name": "[オーセンティック/1928]", "Nickname": "金城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1040, "Id": 104002, "Name": "[秋桜ダンツァトリーチェ]", "Nickname": "金城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1040, "Id": 104003, "Name": "[<PERSON><PERSON>]", "Nickname": "金城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1041, "Id": 104101, "Name": "[サクラ、すすめ！]", "Nickname": "进王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1041, "Id": 104102, "Name": "[レッドホット☆リーダー]", "Nickname": "进王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1042, "Id": 104201, "Name": "[Rocket☆Star]", "Nickname": "采珠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1042, "Id": 104202, "Name": "[Be♪Witched]", "Nickname": "采珠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1043, "Id": 104301, "Name": "[Wicked Punk]", "Nickname": "新光"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1044, "Id": 104401, "Name": "[プラタナス・ウィッチ]", "Nickname": "东商"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1044, "Id": 104402, "Name": "[リアライズ・ルーン]", "Nickname": "东商"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1045, "Id": 104501, "Name": "[マーマリングストリーム]", "Nickname": "溪流"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1045, "Id": 104502, "Name": "[シフォンリボンマミー]", "Nickname": "溪流"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1045, "Id": 104503, "Name": "[慈雨華影]", "Nickname": "溪流"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1046, "Id": 104601, "Name": "[あぶそりゅーと☆LOVE]", "Nickname": "寄子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1046, "Id": 104602, "Name": "[黄昏トライアンフ]", "Nickname": "寄子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1046, "Id": 104603, "Name": "[ルミナス☆トワラー]", "Nickname": "寄子"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1047, "Id": 104701, "Name": "[Heroic Author]", "Nickname": "荒漠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1047, "Id": 104702, "Name": "[Inlaid Stories]", "Nickname": "荒漠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1048, "Id": 104801, "Name": "[ポップス☆ジョーカー]", "Nickname": "佐敦"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1048, "Id": 104802, "Name": "[Aurore☆Vacances]", "Nickname": "佐敦"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1049, "Id": 104901, "Name": "[死中求活]", "Nickname": "庆典"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1050, "Id": 105001, "Name": "[Nevertheless]", "Nickname": "大进"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1050, "Id": 105002, "Name": "[ディファレンス・エンジニア]", "Nickname": "大进"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1050, "Id": 105003, "Name": "[迷光オーヴァライド]", "Nickname": "大进"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1051, "Id": 105101, "Name": "[ティアード・ペタル]", "Nickname": "西野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1051, "Id": 105102, "Name": "[Sweet Juneberry]", "Nickname": "西野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1052, "Id": 105201, "Name": "[うららん一等賞♪]", "Nickname": "春丽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1052, "Id": 105202, "Name": "[初うらら♪さくさくら]", "Nickname": "春丽"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1053, "Id": 105301, "Name": "[黒鉄の大志]", "Nickname": "青竹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1053, "Id": 105302, "Name": "[Ultra☆Marine]", "Nickname": "青竹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1054, "Id": 105401, "Name": "[疾風ペガサス・零式]", "Nickname": "微光"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1055, "Id": 105501, "Name": "[☆キラ★ドキ☆ワク∞マベ∞]", "Nickname": "周日"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1056, "Id": 105601, "Name": "[運気上昇☆幸福万来]", "Nickname": "福来"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1056, "Id": 105602, "Name": "[吉兆・初あらし]", "Nickname": "福来"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1057, "Id": 105701, "Name": "[<PERSON>]", "Nickname": "ＣＢ"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1057, "Id": 105702, "Name": "[絢爛花道歌舞く君]", "Nickname": "ＣＢ"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1058, "Id": 105801, "Name": "[ブルー/レイジング]", "Nickname": "怒涛"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1058, "Id": 105802, "Name": "[Dot-o'-<PERSON>]", "Nickname": "怒涛"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1059, "Id": 105901, "Name": "[ツイステッド・ライン]", "Nickname": "多伯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1059, "Id": 105902, "Name": "[バカンス・サフィール]", "Nickname": "多伯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1060, "Id": 106001, "Name": "[ポインセチア・リボン]", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1060, "Id": 106002, "Name": "[RUN＆WIN]", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1060, "Id": 106003, "Name": "[ネガイノカサネ]", "Nickname": "内恰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1061, "Id": 106101, "Name": "[キング・オブ・エメラルド]", "Nickname": "圣王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1061, "Id": 106102, "Name": "[白く気高き激励の装]", "Nickname": "圣王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1061, "Id": 106103, "Name": "[Evergreen Identity]", "Nickname": "圣王"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1062, "Id": 106201, "Name": "[ぱんぱかティルトット]", "Nickname": "诗歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1062, "Id": 106202, "Name": "[ブルー・タービュランス]", "Nickname": "诗歌"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1063, "Id": 106301, "Name": "[Mantle of Steel]", "Nickname": "生野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1064, "Id": 106401, "Name": "[Line Breakthrough]", "Nickname": "善信"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1064, "Id": 106402, "Name": "[赤心のトナカイさん]", "Nickname": "善信"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1065, "Id": 106501, "Name": "[Fun☆Fun☆ぱりない]", "Nickname": "太阳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1065, "Id": 106502, "Name": "[Joyful Jamboree!]", "Nickname": "太阳"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1066, "Id": 106601, "Name": "[爆走！ターボエンジン]", "Nickname": "涡轮"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1067, "Id": 106701, "Name": "[Natural Brilliance]", "Nickname": "光钻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1067, "Id": 106702, "Name": "[花形・弥栄之翠]", "Nickname": "光钻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1067, "Id": 106703, "Name": "[シュヴァリエ・ブル]", "Nickname": "光钻"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1068, "Id": 106801, "Name": "[錦上・大判御輿]", "Nickname": "北黑"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1068, "Id": 106802, "Name": "[真打・慶鶴之志]", "Nickname": "北黑"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1068, "Id": 106803, "Name": "[結願のしまい華]", "Nickname": "北黑"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1069, "Id": 106901, "Name": "[日下開山・花あかり]", "Nickname": "千代"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1069, "Id": 106902, "Name": "[<PERSON><PERSON><PERSON>]", "Nickname": "千代"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1070, "Id": 107001, "Name": "[<PERSON><PERSON><PERSON><PERSON>]", "Nickname": "天狼"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1070, "Id": 107002, "Name": "[<PERSON><PERSON> stel<PERSON>]", "Nickname": "天狼"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1071, "Id": 107101, "Name": "[クリノクロア・ライン]", "Nickname": "尔丹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1071, "Id": 107102, "Name": "[<PERSON><PERSON><PERSON>]", "Nickname": "尔丹"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1072, "Id": 107201, "Name": "[四白流星の襲]", "Nickname": "八重"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1072, "Id": 107202, "Name": "[黒将Zen]", "Nickname": "八重"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1073, "Id": 107301, "Name": "[志、高し、強し！]", "Nickname": "鹤丸"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1074, "Id": 107401, "Name": "[ブリュニサージュ・ライン]", "Nickname": "光<PERSON>"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1074, "Id": 107402, "Name": "[清らに星澄むスノーロリィタ]", "Nickname": "光<PERSON>"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1076, "Id": 107601, "Name": "[<PERSON><PERSON> le rêve]", "Nickname": "桂冠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1077, "Id": 107701, "Name": "[The Proud Road]", "Nickname": "成田"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1077, "Id": 107702, "Name": "[Celestial Road]", "Nickname": "成田"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1078, "Id": 107801, "Name": "[Fluttertail Spirit]", "Nickname": "也文"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1078, "Id": 107802, "Name": "[<PERSON><PERSON>]", "Nickname": "也文"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1079, "Id": 107901, "Name": "[衣錦還郷の瑞星]", "Nickname": "狂怒"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1080, "Id": 108001, "Name": "[ZOKU-ZOKU GIZMO]", "Nickname": "创升"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1082, "Id": 108201, "Name": "[Looking Fly!]", "Nickname": "北飞"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1083, "Id": 108301, "Name": "[Onyx Soldier]", "Nickname": "吉兆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1083, "Id": 108302, "Name": "[Jetblack Automaton]", "Nickname": "吉兆"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1084, "Id": 108401, "Name": "[身に纏うケラヴノス]", "Nickname": "谷野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1084, "Id": 108402, "Name": "[With a Twist]", "Nickname": "谷野"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1085, "Id": 108501, "Name": "[華麗なる紅玉]", "Nickname": "红宝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1085, "Id": 108502, "Name": "[Flowing Blue]", "Nickname": "红宝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1086, "Id": 108601, "Name": "[オニキス・ライン]", "Nickname": "高峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1086, "Id": 108602, "Name": "[Untouchable <PERSON>]", "Nickname": "高峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1087, "Id": 108701, "Name": "[Flare]", "Nickname": "真弓"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1087, "Id": 108702, "Name": "[溶けない砂糖菓子]", "Nickname": "真弓"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1088, "Id": 108801, "Name": "[黒翠千里行]", "Nickname": "皇冠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1089, "Id": 108901, "Name": "[Grand itinéraire]", "Nickname": "高尚"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1090, "Id": 109001, "Name": "[Le beau sommet]", "Nickname": "极峰"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1091, "Id": 109101, "Name": "[Voyage étincelant]", "Nickname": "强击"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1093, "Id": 109301, "Name": "[Prism]", "Nickname": "凯斯"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1094, "Id": 109401, "Name": "[王者の喊声]", "Nickname": "宝穴"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1096, "Id": 109601, "Name": "[紅色塗早駆具足]", "Nickname": "莫名"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1098, "Id": 109801, "Name": "[陰陽八卦☆開運衣]", "Nickname": "小林"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1098, "Id": 109802, "Name": "[光彩陸離☆招福衣]", "Nickname": "小林"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1099, "Id": 109901, "Name": "[スター・ライト・シップ]", "Nickname": "北港"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1099, "Id": 109902, "Name": "[パステルマリン・ロコドル]", "Nickname": "北港"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1100, "Id": 110001, "Name": "[<PERSON>]", "Nickname": "奇锐"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1102, "Id": 110201, "Name": "[リトモ・デッラ・テッラ]", "Nickname": "万籁"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1104, "Id": 110401, "Name": "[登竜之頂]", "Nickname": "葛城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1104, "Id": 110402, "Name": "[雅号・墨龍]", "Nickname": "葛城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1105, "Id": 110501, "Name": "[<PERSON>-<PERSON><PERSON>]", "Nickname": "新宇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1105, "Id": 110502, "Name": "[Like “ZEER”]", "Nickname": "新宇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1106, "Id": 110601, "Name": "[みらくるめーくあっぷ！]", "Nickname": "奇宝"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1107, "Id": 110701, "Name": "[GLITTER!]", "Nickname": "舞城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1107, "Id": 110702, "Name": "[Tap! Tap! Tap!]", "Nickname": "舞城"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1108, "Id": 110801, "Name": "[Red in Black]", "Nickname": "大锤"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1109, "Id": 110901, "Name": "[Dream Successor]", "Nickname": "莱茵"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1110, "Id": 111001, "Name": "[<PERSON>]", "Nickname": "西沙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1110, "Id": 111002, "Name": "[<PERSON><PERSON>]", "Nickname": "西沙"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1111, "Id": 111101, "Name": "[Inherited Hope]", "Nickname": "救主"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1115, "Id": 111501, "Name": "[総攬]", "Nickname": "巨匠"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1116, "Id": 111601, "Name": "[Regina dei fiori]", "Nickname": "贵妇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1117, "Id": 111701, "Name": "[ドラマティック・チュチュ]", "Nickname": "芭蕾"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1119, "Id": 111901, "Name": "[夢路のよすが]", "Nickname": "梦旅"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1120, "Id": 112001, "Name": "[真実一路]", "Nickname": "金镇"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1121, "Id": 112101, "Name": "[<PERSON>]", "Nickname": "多旺"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1124, "Id": 112401, "Name": "[POPPING!]", "Nickname": "吹波"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1127, "Id": 112701, "Name": "[義心の黒焔]", "Nickname": "超常"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1131, "Id": 113101, "Name": "[すまいる・まい・うぇい！]", "Nickname": "放声"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1132, "Id": 113201, "Name": "[9927 Wishes]", "Nickname": "唯爱"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1133, "Id": 113301, "Name": "[陸離の編纂者]", "Nickname": "创世"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1001, "Id": 9100101, "Name": "[スペシャルドリーマー]", "Nickname": "特别"}, {"$type": "UmamusumeResponseAnalyzer.Entities.UmaName, UmamusumeResponseAnalyzer", "CharaId": 1011, "Id": 9101101, "Name": "[岩穿つ青]", "Nickname": "草上"}]}