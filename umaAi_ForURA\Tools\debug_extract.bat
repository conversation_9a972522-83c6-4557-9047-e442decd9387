@echo off
echo DEBUG: Data Extraction Tool Launcher
echo =====================================

REM Show current directory
echo DEBUG: Current directory: %CD%
echo DEBUG: Script directory: %~dp0

REM Change to script directory
cd /d "%~dp0"
echo DEBUG: Changed to: %CD%

REM List files in current directory
echo DEBUG: Files in current directory:
dir /b *.py *.bat

REM Check if Python is in PATH
echo.
echo DEBUG: Checking Python...
where python
if %errorlevel% neq 0 (
    echo DEBUG: Python not found in PATH
    echo DEBUG: Trying py command...
    where py
    if %errorlevel% neq 0 (
        echo ERROR: Neither python nor py command found
        echo Please install Python and add to PATH
        goto :error_exit
    ) else (
        echo DEBUG: Using py command instead of python
        set PYTHON_CMD=py
    )
) else (
    echo DEBUG: Python found in PATH
    set PYTHON_CMD=python
)

REM Show Python version
echo DEBUG: Python version:
%PYTHON_CMD% --version
if %errorlevel% neq 0 (
    echo ERROR: Failed to get Python version
    goto :error_exit
)

REM Check if script file exists
echo.
echo DEBUG: Checking for simple_br_extractor.py...
if exist "simple_br_extractor.py" (
    echo DEBUG: Script file found
) else (
    echo ERROR: simple_br_extractor.py not found
    echo Current directory contents:
    dir
    goto :error_exit
)

REM Check brotli library
echo.
echo DEBUG: Checking brotli library...
%PYTHON_CMD% -c "import brotli; print('Brotli version:', brotli.__version__)" 2>nul
if %errorlevel% neq 0 (
    echo DEBUG: Brotli not installed, attempting to install...
    %PYTHON_CMD% -m pip install brotli
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install brotli
        echo Try running: pip install brotli
        goto :error_exit
    )
    echo DEBUG: Brotli installed successfully
) else (
    echo DEBUG: Brotli library is available
)

REM Test script execution
echo.
echo DEBUG: Testing script execution...
%PYTHON_CMD% -c "print('Python execution test successful')"
if %errorlevel% neq 0 (
    echo ERROR: Python execution test failed
    goto :error_exit
)

echo.
echo DEBUG: All checks passed!
echo DEBUG: Starting extraction tool...
echo =====================================

REM Run the actual script
if "%1"=="" (
    echo Starting interactive mode...
    %PYTHON_CMD% simple_br_extractor.py
) else (
    echo Starting with file: %1
    if "%2"=="" (
        %PYTHON_CMD% simple_br_extractor.py "%1"
    ) else (
        %PYTHON_CMD% simple_br_extractor.py "%1" "%2"
    )
)

echo.
echo DEBUG: Script execution completed
echo Return code: %errorlevel%
goto :normal_exit

:error_exit
echo.
echo DEBUG: Error occurred, stopping execution
echo Please check the error messages above
echo.
echo Possible solutions:
echo 1. Install Python 3.8+ from https://python.org
echo 2. Make sure Python is added to PATH during installation
echo 3. Run 'pip install brotli' in command prompt
echo 4. Make sure you're running this from the correct directory
echo.

:normal_exit
echo.
echo Press any key to exit...
pause >nul
