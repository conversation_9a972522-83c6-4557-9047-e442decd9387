@echo off
echo C++ Practice Compiler
echo =====================

REM Check if we have a specific file to compile
if "%1"=="" (
    echo Usage: compile_practice.bat practice_file_name
    echo Example: compile_practice.bat practice1_1
    echo.
    echo Available practice files:
    dir /b *.cpp
    echo.
    pause
    exit /b 1
)

set filename=%1
if not exist "%filename%.cpp" (
    echo Error: %filename%.cpp not found!
    pause
    exit /b 1
)

echo Compiling %filename%.cpp...

REM Compile the practice file
cl /EHsc /std:c++17 "%filename%.cpp" /Fe:"%filename%.exe" /nologo

if %errorlevel% equ 0 (
    echo.
    echo ✅ Compilation successful!
    echo Generated: %filename%.exe
    echo.
    echo Running the program:
    echo ==================
    "%filename%.exe"
) else (
    echo.
    echo ❌ Compilation failed!
    echo Please check your code for errors.
)

echo.
pause
