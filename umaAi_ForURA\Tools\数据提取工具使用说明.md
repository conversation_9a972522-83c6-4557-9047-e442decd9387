# 🏇 赛马娘数据提取工具使用说明

## 🎯 工具功能

这个工具可以解压赛马娘游戏的.br格式数据文件，支持：
- **事件数据** - events.br
- **支援卡数据** - support_card.br, card.br等
- **角色数据** - character.br, uma.br, chara.br等
- **技能数据** - skill.br
- **比赛数据** - race.br
- **其他游戏数据文件**

## 🚀 快速开始

### 方法1：双击运行（推荐）
```cmd
# 直接双击运行
extract_br.bat
```

### 方法2：命令行运行
```cmd
# 单文件解压（自动输出路径）
extract_br.bat "C:\path\to\your\file.br"

# 单文件解压（指定输出路径）
extract_br.bat "C:\path\to\your\file.br" "C:\output\path\extracted.json"

# 或者直接使用Python
python simple_br_extractor.py "C:\path\to\your\file.br"
```

## 📋 使用模式

### 🔧 交互模式（推荐新手）

运行`extract_br.bat`后选择模式：

```
请选择操作模式:
1. 单文件解压    # 解压一个.br文件
2. 批量解压      # 解压整个文件夹的所有.br文件
3. 退出
```

#### 单文件解压流程：
1. **输入文件路径** - 输入要解压的.br文件完整路径
2. **选择输出路径** - 可以使用默认路径或自定义
3. **确认执行** - 确认后开始解压
4. **查看结果** - 解压完成后查看JSON文件

#### 批量解压流程：
1. **输入目录路径** - 输入包含.br文件的文件夹路径
2. **选择输出目录** - 默认在输入目录下创建extracted文件夹
3. **自动处理** - 工具会自动处理所有.br文件
4. **查看统计** - 显示成功/失败的文件数量

### ⚡ 命令行模式（推荐高级用户）

```cmd
# 基本用法
python simple_br_extractor.py input_file.br

# 指定输出文件
python simple_br_extractor.py input_file.br output_file.json

# 使用批处理脚本
extract_br.bat "C:\data\events.br"
extract_br.bat "C:\data\events.br" "C:\output\events_data.json"
```

## 📁 文件路径示例

### 常见的.br文件位置
```
# 如果您有游戏数据文件夹
C:\GameData\events.br
C:\GameData\support_card.br
C:\GameData\character.br

# 如果文件在项目目录
C:\Users\<USER>\Desktop\URA源代码\umaAi_ForURA\events\events.br
```

### 输出文件示例
```
# 默认输出（自动生成）
events.br → events_extracted.json
support_card.br → support_card_extracted.json
character.br → character_extracted.json

# 自定义输出
events.br → game_events.json
support_card.br → support_cards_data.json
```

## 🔍 工具特性

### ✅ 智能文件类型识别
工具会根据文件名自动识别数据类型：
- `events.br` → 事件数据
- `support.br`, `card.br` → 支援卡数据
- `character.br`, `uma.br`, `chara.br` → 角色数据
- `skill.br` → 技能数据
- `race.br` → 比赛数据

### ✅ 多种解压方法
- **Brotli解压** - 主要方法，适用于大多数.br文件
- **Gzip解压** - 备用方法
- **Zlib解压** - 备用方法
- **原始数据分析** - 处理未压缩的文件

### ✅ 详细的数据分析
解压后会自动分析JSON数据结构：
- 显示数据类型和项目数量
- 识别可能的游戏相关字段
- 提供数据结构概览

### ✅ 错误处理和重试
- 文件不存在时提示重新输入
- 解压失败时尝试多种方法
- 详细的错误信息和建议

## 📊 输出文件格式

### JSON数据结构示例

**事件数据** (events_extracted.json):
```json
[
  {
    "Id": 100001,
    "Name": "イベント名",
    "TriggerName": "URA",
    "Choices": [
      {
        "Option": "選択肢1",
        "SuccessEffect": "体力+30、やる気↑",
        "SuccessEffectValue": {
          "Values": [10, 0, 0, 0, 0, 20, 10, 0, 0, 1, ...]
        }
      }
    ]
  }
]
```

**支援卡数据** (support_card_extracted.json):
```json
[
  {
    "Id": 30001,
    "Name": "支援卡名称",
    "Rarity": 3,
    "Type": 1,
    "StatusBonus": [15, 0, 0, 0, 0],
    "TrainingBonus": 10
  }
]
```

## 🛠️ 故障排除

### 常见问题

**Q: 提示"未找到Python"**
A: 请安装Python 3.8+，并确保添加到系统PATH

**Q: 提示"brotli库安装失败"**
A: 手动运行 `pip install brotli`

**Q: 文件解压失败**
A: 检查文件是否损坏，或者文件格式是否正确

**Q: 输出的JSON文件很大**
A: 这是正常的，游戏数据通常包含大量信息

**Q: 中文显示乱码**
A: 确保使用支持UTF-8的文本编辑器打开JSON文件

### 调试模式

如果遇到问题，可以查看详细输出：
```cmd
python simple_br_extractor.py your_file.br
```

工具会显示：
- 文件大小和类型
- 尝试的解压方法
- 数据结构分析
- 详细的错误信息

## 📈 使用建议

### 新手用户
1. **从单文件开始** - 先解压一个events.br文件熟悉流程
2. **使用交互模式** - 更友好的用户界面
3. **检查输出文件** - 用文本编辑器查看解压结果
4. **保存重要数据** - 备份解压后的JSON文件

### 高级用户
1. **使用命令行模式** - 更高效的批量处理
2. **编写批处理脚本** - 自动化数据处理流程
3. **分析数据结构** - 理解游戏数据的组织方式
4. **集成到项目** - 将解压的数据用于AI开发

### 开发者
1. **修改源代码** - 根据需要调整`simple_br_extractor.py`
2. **添加新的文件类型** - 在`get_file_type_hint`函数中添加
3. **自定义输出格式** - 修改`save_decompressed_data`函数
4. **集成到构建流程** - 将数据提取作为构建步骤

## 🎯 下一步

解压数据后，您可以：

1. **分析数据结构** - 理解游戏机制
2. **咨询开发者** - 确认数据字段含义
3. **集成到AI项目** - 将数据用于游戏模拟
4. **开发新功能** - 基于真实数据改进AI

## 📞 获取帮助

如果遇到问题：
1. 查看本文档的故障排除部分
2. 检查Python和依赖库是否正确安装
3. 确认.br文件是否完整和正确
4. 查看工具输出的详细错误信息

---

**工具版本**: v2.0  
**支持的文件格式**: .br (Brotli压缩)  
**输出格式**: JSON  
**Python要求**: 3.8+  
**依赖库**: brotli
