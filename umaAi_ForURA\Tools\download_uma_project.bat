@echo off
echo Downloading UmamusumeDeserializeDB5 Project
echo ==========================================

REM Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    echo Please install Python 3.8+
    pause
    exit /b 1
)

REM Install requests if needed
python -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing requests library...
    pip install requests
)

REM Run download script
echo Starting download...
python download_github_project.py "https://github.com/UmamusumeResponseAnalyzer/UmamusumeDeserializeDB5"

echo.
echo Download completed!
pause
