# URA剧本AI项目开发记忆文档

## 📋 项目概述

**项目名称**: 赛马娘URA剧本AI  
**开发目标**: 基于UmaAI的设计思路，专门为URA剧本开发AI系统  
**项目路径**: `C:\Users\<USER>\Desktop\URA源代码\umaAi_ForURA`  
**开发状态**: 基础框架完成，需要集成真实数据和改进AI

## 🎯 项目选择理由

**为什么选择URA剧本**:
- ✅ 最基础的剧本，机制简单清晰
- ✅ 没有复杂的特殊系统（如UAF的颜色训练）
- ✅ 数据最完整，攻略资料丰富
- ✅ 适合作为学习游戏AI的入门项目
- ✅ 78回合固定流程，容易理解和调试

**与其他剧本对比**:
- UAF剧本：过于复杂，有颜色训练、Buff系统等
- Grand Masters：有大师训练系统，相对复杂
- Aoharu杯：有团队机制，策略性强但复杂

## 🏗️ 项目架构

### 当前项目结构
```
umaAi_ForURA/
├── GameSimulator/           # 游戏模拟器
│   ├── Game.h              # 游戏状态定义（已完成）
│   └── Game.cpp            # 游戏逻辑实现（已完成）
├── Tools/
│   ├── test_game.cpp       # 测试程序+简单AI（已完成）
│   ├── simple_br_extractor.py  # 数据提取工具（已完成）
│   └── events_extracted.json   # 提取的事件数据
├── Practice/               # C++学习练习
├── events/
│   └── events.br          # 原始事件数据文件
├── Config/
│   └── game_config.json   # 游戏配置
├── build.bat              # 编译脚本（英文版）
├── compile.cmd            # 简化编译脚本
└── README.md              # 项目说明
```

### 核心组件状态

**✅ 已完成**:
- 基础游戏状态结构 (`URAGame`)
- 游戏模拟器 (`URAGameSimulator`)
- 简单AI (`SimpleSmartAI`)
- 测试框架和性能评估
- 数据提取工具
- C++学习文档和练习题

**🔄 进行中**:
- 事件数据解析和集成
- 支援卡系统实现

**📋 待完成**:
- 神经网络AI模型
- 自对弈训练系统
- 用户界面

## 🎮 游戏机制实现

### URA剧本特点
- **总回合数**: 78回合
- **训练系统**: 5种基础训练（速度、耐力、力量、根性、智慧）+ 休息 + 外出
- **比赛系统**: 9场固定比赛（回合13,24,37,48,61,72,75,76,77）
- **支援卡**: 最多6张，提供训练加成和友情度系统
- **事件系统**: 随机事件和支援卡事件

### 当前实现状态

**游戏状态** (`URAGame`结构体):
```cpp
struct URAGame {
    int turn;                    // 当前回合 (0-77)
    int vital;                   // 体力值 (0-100)
    int motivation;              // 干劲等级 (1-5)
    int speed, stamina, power, guts, wisdom;  // 五维属性
    int skill_points;            // 技能点
    int fan_count;               // 粉丝数
    vector<SupportCard> support_cards;  // 支援卡系统
    // ... 其他状态
};
```

**AI策略** (`SimpleSmartAI`):
- 体力 < 30 时休息
- 优先训练比赛重要属性（速度、耐力、力量）到400
- 否则训练最低属性
- 简单的事件选择逻辑

## 📊 当前性能表现

**最新测试结果** (100局游戏):
- 平均评分: 2264.31
- 最高评分: 3533
- 最低评分: 1635
- 比赛胜率: 0% → 已调整胜利条件，待重新测试

**性能分析**:
- ✅ AI稳定运行，无崩溃
- ✅ 评分合理，属性发展正常
- ❌ 比赛胜率过低，需要优化胜利条件和AI策略

## 🔧 开发环境配置

**已配置环境**:
- Visual Studio 2022 Community
- Developer Command Prompt
- Python 3.x + brotli库（用于数据提取）
- Git（用于下载GitHub项目）

**编译方式**:
```cmd
# 方法1：使用脚本
compile.cmd

# 方法2：手动编译
cl /EHsc /std:c++17 GameSimulator\Game.cpp Tools\test_game.cpp /Fe:bin\test_game.exe
```

**运行测试**:
```cmd
cd bin
test_game.exe
# 选择1-3: 详细模式/快速模式/性能测试
```

**数据提取工具**:
```cmd
# 使用独立的数据提取工具
cd DataExtractor
extract.bat
# 自动保存到Database目录
```

## 📁 真实数据集成

### 已提取数据
**events.br文件**:
- ✅ 成功解压为JSON格式
- 包含14,122个事件项目
- 数据结构：`Id`, `Name`, `TriggerName`, `Choices`
- 每个选择包含：`Option`, `SuccessEffect`, `SuccessEffectValue`

### 🎯 重大发现：UmamusumeDeserializeDB5项目分析

**events.br文件的真实来源**:
- ❗ **events.br不是游戏原始数据**
- ✅ **是UmamusumeDeserializeDB5工具生成的**
- 🔄 **数据流程**: 游戏数据库(master.mdb) + 网络攻略数据 → 文本解析 → 数值化 → events.br

**SuccessEffectValue.Values数组的确切含义**:
```json
"SuccessEffectValue": {
    "Values": [10, 0, 0, 0, 0, 20, 0, 30, 0, 1]
}
```

**Values数组10个位置的映射**:
- `Values[0]` = 速度 (スピード)
- `Values[1]` = 耐力 (スタミナ)
- `Values[2]` = 力量 (パワー)
- `Values[3]` = 根性 (根性)
- `Values[4]` = 智慧 (賢さ)
- `Values[5]` = 技能点 (スキルPt)
- `Values[6]` = 技能提示 (ヒント)
- `Values[7]` = 体力 (体力)
- `Values[8]` = 羁绊 (絆)
- `Values[9]` = 干劲 (やる気)

**文本解析核心逻辑**:
```csharp
// 示例: "体力+30、やる気↑、全ステータス+10"
// 解析结果: [10,10,10,10,10,0,0,30,0,1]
// 含义: 全属性+10, 体力+30, 干劲+1
```

**TriggerName生成规则**:
- `"URA"` = URA剧本通用事件
- `"[剧情标题]角色名"` = 角色专属事件
- `""` (空) = 全局通用事件

### 已解决的关键问题
- ✅ **SuccessEffectValue含义** - 完全明确了10个位置的映射
- ✅ **数据生成过程** - 理解了从文本到数值的转换
- ✅ **事件分类机制** - 明确了TriggerName的含义
- ✅ **null值原因** - 空效果文本导致FailedEffectValue为null

## 🎓 学习资源

**已创建C++学习体系**:
- `C++游戏AI开发教学文档.md` - 完整教学文档
- `C++游戏AI开发练习题.md` - 系统化练习题
- `Practice/` 目录 - 练习环境和答案

**学习进度建议**:
- 第1周：基础语法和结构体
- 第2周：容器和函数
- 第3周：高级功能（随机数、文件操作）
- 第4周：综合应用和AI实现

## 🚀 下一步计划

### 短期目标 (1-2周)
1. **✅ 已完成：分析UmamusumeDeserializeDB5项目** - 完全理解了数据结构
2. **实现准确的事件系统** - 基于正确的Values数组映射
3. **更新GameSimulator事件处理** - 使用真实的属性计算逻辑
4. **改进AI事件选择策略** - 基于准确的效果数值进行决策

### 中期目标 (1-2月)
1. **集成UmamusumeDeserializeDB5** - 获取最新游戏数据
2. **实现完整的事件系统** - 包括所有事件类型和效果
3. **学习神经网络基础** - 理解深度学习原理
4. **实现Python训练脚本** - 创建神经网络模型

### 长期目标 (2-3月)
1. **自对弈训练系统** - 让AI自己学习
2. **高级搜索算法** - 实现MCTS
3. **用户界面** - 创建图形化界面
4. **扩展到其他剧本** - 适配UAF、Grand Masters等

## 💡 关键经验和教训

### 成功经验
1. **选择简单剧本** - URA剧本确实比UAF简单很多
2. **循序渐进** - 从基础功能开始，逐步完善
3. **重视测试** - 完整的测试框架帮助发现问题
4. **真实数据** - 获取真实游戏数据是关键
5. **深入分析开源项目** - 通过分析UmamusumeDeserializeDB5完全理解了数据结构

### 遇到的挑战
1. **✅ 已解决：数据格式理解** - 通过分析源项目完全理解了.br文件生成过程
2. **比赛平衡性** - 初始的胜利条件过于严格
3. **C++语法** - 需要系统学习C++语法
4. **机器学习知识** - 需要学习神经网络基础

### 解决方案
1. **✅ 完成：创建数据提取工具** - 成功解析.br文件并理解数据结构
2. **调整游戏参数** - 优化比赛胜利条件
3. **制作学习资料** - 针对性的C++教程和练习
4. **分析开源项目** - 通过UmamusumeDeserializeDB5项目获得关键洞察
5. **分阶段学习** - 先掌握基础，再学习高级概念

## 🔗 重要文件和路径

**核心代码文件**:
- `GameSimulator/Game.h` - 游戏状态定义
- `GameSimulator/Game.cpp` - 游戏逻辑实现
- `Tools/test_game.cpp` - 测试程序和AI

**数据文件**:
- `events/events.br` - 原始事件数据
- `Tools/events_extracted.json` - 解析后的事件数据

**学习资料**:
- `C++游戏AI开发教学文档.md`
- `C++游戏AI开发练习题.md`
- `Practice/` - 练习环境

**配置文件**:
- `Config/game_config.json` - 游戏参数配置
- `compile.cmd` - 编译脚本

## 🔗 重要文件和路径

**核心代码文件**:
- `GameSimulator/Game.h` - 游戏状态定义
- `GameSimulator/Game.cpp` - 游戏逻辑实现
- `Tools/test_game.cpp` - 测试程序和AI

**数据文件**:
- `events/events.br` - 原始事件数据（由UmamusumeDeserializeDB5生成）
- `Database/events_extracted.json` - 解析后的事件数据
- `Database/` - 所有提取的游戏数据

**数据提取工具**:
- `DataExtractor/br_extractor.py` - 独立的.br文件提取工具
- `DataExtractor/extract.bat` - 一键启动脚本
- `DataExtractor/README.md` - 使用说明

**项目分析文档**:
- `UmamusumeDeserializeDB5项目分析.md` - 关键的数据结构分析
- `项目开发记忆文档.md` - 完整的开发记录

**学习资料**:
- `C++游戏AI开发教学文档.md`
- `C++游戏AI开发练习题.md`
- `Practice/` - 练习环境

**配置文件**:
- `Config/game_config.json` - 游戏参数配置
- `compile.cmd` - 编译脚本
- `项目结构说明.md` - 项目结构文档

## 📞 联系和协作

**✅ 已完成的开发者咨询**:
- 确认了events.br是UmamusumeDeserializeDB5生成的数据
- 理解了SuccessEffectValue的生成过程
- 明确了Values数组的10个位置含义

**项目分享**:
- 代码托管在本地，可以打包分享
- 测试结果和性能数据已记录
- 学习资料可以独立使用
- 数据结构分析文档可供参考

## 🤖 AI设计思路

### 基于UmaAI的设计理念
**UmaAI的成功要素**:
- 游戏状态的完整建模
- 神经网络 + 搜索算法结合
- 自对弈训练生成数据
- 针对特定剧本的优化

**我们的实现方案**:
1. **简化但完整的状态表示** - URAGame结构体
2. **分层AI架构** - 规则AI → 神经网络AI → 搜索算法
3. **渐进式开发** - 先基础功能，再高级算法
4. **数据驱动** - 使用真实游戏数据

### 当前AI架构
```cpp
// 简单规则AI (已实现)
class SimpleSmartAI {
    Action selectAction(const URAGameSimulator& simulator);
    // 基于规则的决策逻辑
};

// 计划中的神经网络AI
class NeuralNetworkAI {
    // PyTorch模型 → ONNX → C++推理
};

// 计划中的搜索算法
class MCTS_AI {
    // 蒙特卡洛树搜索
};
```

## 🔍 技术细节记录

### 编译环境问题解决
**遇到的问题**:
- 中文编码导致批处理文件乱码
- 跨盘符路径切换失败
- 缺少Developer Command Prompt环境

**解决方案**:
- 创建英文版编译脚本
- 使用`cd /d`命令跨盘符切换
- 提供详细的环境配置指南

### 数据提取技术方案
**工具选择**:
- Python + brotli库解压.br文件
- 自动分析JSON数据结构
- 生成C++代码模板

**提取结果**:
```python
# 成功提取14,122个事件
# 数据结构清晰：Id, Name, TriggerName, Choices
# Values数组包含20个数值，含义待确认
```

### 性能优化记录
**已实施的优化**:
1. 调整比赛胜利条件：`800 + turn * 5` → `600 + turn * 3`
2. 改进AI策略：优先训练比赛重要属性
3. 使用现代C++特性：`std::mt19937`随机数生成

**待优化项目**:
- 训练收益公式的真实性
- 支援卡加成的准确计算
- 事件触发概率的调整

## 📚 学习进度跟踪

### C++学习状态
**已掌握概念**:
- 基础语法和变量声明
- 结构体和类的使用
- 容器（vector）和数组
- 函数定义和调用
- 枚举和常量

**学习资料完成度**:
- ✅ 教学文档：9章完整内容
- ✅ 练习题：7个章节21道题目
- ✅ 答案文档：详细解答和解释
- 🔄 实际练习：待用户完成

**建议学习顺序**:
1. 先完成练习1-2（基础语法）
2. 理解项目中的Game.h结构
3. 尝试修改SimpleSmartAI的决策逻辑
4. 完成练习3-4（容器和函数）
5. 开始学习神经网络基础

### 机器学习准备
**需要学习的概念**:
- 神经网络基础原理
- PyTorch框架使用
- 自对弈训练方法
- 模型评估和优化

**学习资源规划**:
- 在线教程和文档
- 实际代码练习
- 小规模模型实验
- 逐步集成到项目中

## 🎯 具体待办事项

### 立即行动项 (本周)
- [ ] 咨询开发者关于数据结构问题
- [ ] 完成C++练习1-2
- [ ] 重新测试调整后的AI性能
- [ ] 整理支援卡数据需求

### 短期目标 (2周内)
- [ ] 实现真实的事件系统
- [ ] 集成支援卡数据和加成计算
- [ ] 优化AI决策逻辑
- [ ] 完成C++练习3-4

### 中期目标 (1月内)
- [ ] 学习PyTorch基础
- [ ] 实现简单的神经网络模型
- [ ] 生成自对弈训练数据
- [ ] 完成所有C++练习

### 长期目标 (3月内)
- [ ] 完整的神经网络AI
- [ ] MCTS搜索算法
- [ ] 用户界面开发
- [ ] 性能优化和发布

## 🔧 开发工具和资源

### 开发环境
- **IDE**: Visual Studio 2022 Community
- **编译器**: MSVC 19.44.35211
- **Python**: 3.x + brotli, requests, beautifulsoup4
- **版本控制**: Git (本地)

### 参考资源
- **UmaAI项目**: 设计思路和架构参考
- **cppreference.com**: C++语法参考
- **PyTorch官方文档**: 深度学习框架
- **游戏攻略网站**: 数据验证和机制理解

### 测试工具
- **性能测试**: 100局游戏统计
- **单元测试**: 各模块功能验证
- **调试工具**: Visual Studio调试器
- **数据分析**: Python脚本分析结果

---

**文档创建时间**: 2025年1月22日
**最后更新**: 2025年1月22日 - 重大突破：完全理解events.br数据结构
**文档用途**: 为其他AI助手提供完整的项目上下文
**重大里程碑**:
- ✅ 通过分析UmamusumeDeserializeDB5项目完全理解了数据结构
- ✅ 明确了SuccessEffectValue.Values数组的10个位置含义
- ✅ 理解了从文本效果到数值的转换过程
- ✅ 项目进入精确实现阶段

**下次更新**: 实现基于准确数据结构的事件系统后
