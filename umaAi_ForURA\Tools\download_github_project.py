#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub项目下载工具
用于下载GitHub项目到本地
"""

import requests
import zipfile
import os
import sys
from urllib.parse import urlparse

def download_github_project(github_url, download_dir="Downloads"):
    """
    下载GitHub项目
    
    Args:
        github_url: GitHub项目URL
        download_dir: 下载目录
    """
    try:
        print(f"🔗 GitHub项目URL: {github_url}")
        
        # 解析GitHub URL
        if "github.com" not in github_url:
            print("❌ 不是有效的GitHub URL")
            return False
        
        # 提取用户名和项目名
        parts = github_url.strip('/').split('/')
        if len(parts) < 5:
            print("❌ URL格式不正确")
            return False
        
        username = parts[-2]
        repo_name = parts[-1]
        
        print(f"👤 用户: {username}")
        print(f"📦 项目: {repo_name}")
        
        # 构建下载URL
        download_url = f"https://github.com/{username}/{repo_name}/archive/refs/heads/main.zip"
        
        print(f"\n📥 开始下载...")
        print(f"下载地址: {download_url}")
        
        # 创建下载目录
        os.makedirs(download_dir, exist_ok=True)
        
        # 下载文件
        response = requests.get(download_url, stream=True)
        
        if response.status_code == 404:
            # 尝试master分支
            download_url = f"https://github.com/{username}/{repo_name}/archive/refs/heads/master.zip"
            print(f"尝试master分支: {download_url}")
            response = requests.get(download_url, stream=True)
        
        if response.status_code != 200:
            print(f"❌ 下载失败，状态码: {response.status_code}")
            return False
        
        # 保存ZIP文件
        zip_filename = f"{repo_name}.zip"
        zip_path = os.path.join(download_dir, zip_filename)
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0
        
        with open(zip_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"\r📥 下载进度: {progress:.1f}% ({downloaded_size:,}/{total_size:,} 字节)", end='')
        
        print(f"\n✅ 下载完成: {zip_path}")
        
        # 解压文件
        extract_dir = os.path.join(download_dir, repo_name)
        print(f"\n📂 解压到: {extract_dir}")
        
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(download_dir)
        
        # 重命名解压后的文件夹
        extracted_folder = None
        for item in os.listdir(download_dir):
            item_path = os.path.join(download_dir, item)
            if os.path.isdir(item_path) and item.startswith(repo_name):
                extracted_folder = item_path
                break
        
        if extracted_folder and extracted_folder != extract_dir:
            if os.path.exists(extract_dir):
                import shutil
                shutil.rmtree(extract_dir)
            os.rename(extracted_folder, extract_dir)
        
        print(f"✅ 解压完成: {extract_dir}")
        
        # 删除ZIP文件
        os.remove(zip_path)
        print(f"🧹 清理ZIP文件: {zip_filename}")
        
        # 显示项目结构
        print(f"\n📁 项目结构:")
        show_directory_structure(extract_dir, max_depth=2)
        
        return extract_dir
        
    except Exception as e:
        print(f"❌ 下载过程中出错: {e}")
        return False

def show_directory_structure(directory, max_depth=2, current_depth=0, prefix=""):
    """显示目录结构"""
    if current_depth > max_depth:
        return
    
    try:
        items = sorted(os.listdir(directory))
        for i, item in enumerate(items[:10]):  # 只显示前10个项目
            item_path = os.path.join(directory, item)
            is_last = i == len(items) - 1 or i == 9
            
            current_prefix = "└── " if is_last else "├── "
            print(f"{prefix}{current_prefix}{item}")
            
            if os.path.isdir(item_path) and current_depth < max_depth:
                next_prefix = prefix + ("    " if is_last else "│   ")
                show_directory_structure(item_path, max_depth, current_depth + 1, next_prefix)
        
        if len(items) > 10:
            print(f"{prefix}... (还有 {len(items) - 10} 个项目)")
            
    except PermissionError:
        print(f"{prefix}[权限不足]")

def analyze_project_files(project_dir):
    """分析项目文件"""
    print(f"\n🔍 分析项目文件...")
    
    important_files = [
        "README.md", "readme.md", "README.txt",
        "requirements.txt", "package.json", "Cargo.toml",
        "main.py", "app.py", "index.js", "main.cpp",
        "Makefile", "CMakeLists.txt", "build.gradle"
    ]
    
    found_files = []
    for root, dirs, files in os.walk(project_dir):
        for file in files:
            if file in important_files:
                rel_path = os.path.relpath(os.path.join(root, file), project_dir)
                found_files.append(rel_path)
    
    if found_files:
        print("📋 重要文件:")
        for file in found_files:
            print(f"  - {file}")
    
    # 检查编程语言
    languages = {}
    for root, dirs, files in os.walk(project_dir):
        for file in files:
            ext = os.path.splitext(file)[1].lower()
            if ext:
                languages[ext] = languages.get(ext, 0) + 1
    
    if languages:
        print("\n💻 编程语言:")
        sorted_langs = sorted(languages.items(), key=lambda x: x[1], reverse=True)
        for ext, count in sorted_langs[:5]:
            print(f"  {ext}: {count} 个文件")

def main():
    print("🏇 GitHub项目下载工具")
    print("=" * 50)
    
    # 默认项目URL
    default_url = "https://github.com/UmamusumeResponseAnalyzer/UmamusumeDeserializeDB5"
    
    if len(sys.argv) > 1:
        github_url = sys.argv[1]
    else:
        print(f"默认项目: {default_url}")
        github_url = input("GitHub项目URL (回车使用默认): ").strip()
        if not github_url:
            github_url = default_url
    
    # 设置下载目录
    download_dir = "GitHub_Projects"
    print(f"📁 下载目录: {download_dir}")
    
    # 下载项目
    project_dir = download_github_project(github_url, download_dir)
    
    if project_dir:
        print(f"\n🎉 项目下载成功!")
        print(f"📂 项目位置: {os.path.abspath(project_dir)}")
        
        # 分析项目
        analyze_project_files(project_dir)
        
        print(f"\n📖 下一步:")
        print(f"1. 查看 README.md 了解项目说明")
        print(f"2. 检查依赖要求和安装说明")
        print(f"3. 按照项目文档运行程序")
        
        # 询问是否打开项目目录
        try:
            import subprocess
            open_dir = input(f"\n是否打开项目目录? (y/n): ").lower()
            if open_dir == 'y':
                subprocess.run(['explorer', os.path.abspath(project_dir)], shell=True)
        except:
            pass
    else:
        print(f"\n❌ 项目下载失败")
        print(f"💡 您也可以手动下载:")
        print(f"1. 访问: {github_url}")
        print(f"2. 点击绿色的 'Code' 按钮")
        print(f"3. 选择 'Download ZIP'")

if __name__ == "__main__":
    main()
