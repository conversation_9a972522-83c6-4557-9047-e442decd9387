# 🏇 赛马娘数据提取工具

## 📋 功能说明

这是一个专门用于解压赛马娘游戏.br格式数据文件的工具，支持：
- 事件数据 (events.br)
- 支援卡数据 (support_card.br)
- 角色数据 (character.br, names.br)
- 技能数据 (skill.br)
- 比赛数据 (race.br)

## 🚀 快速使用

### 方法1：拖拽使用
将.br文件直接拖到 `extract.bat` 上

### 方法2：双击运行
双击 `extract.bat`，然后输入文件路径

### 方法3：命令行
```cmd
# 基本用法
python br_extractor.py "path/to/file.br"

# 指定输出文件
python br_extractor.py "path/to/file.br" "output.json"
```

## 📁 输出文件

- **JSON格式** - 结构化数据，易于程序处理
- **自动命名** - `filename_extracted.json`
- **数据分析** - 自动分析并显示数据结构

## 🔧 环境要求

- Python 3.8+
- brotli库 (自动安装)

## 📊 支持的文件类型

| 文件名模式 | 数据类型 | 说明 |
|-----------|---------|------|
| events.br | 事件数据 | 游戏事件和选择 |
| support*.br, card*.br | 支援卡数据 | 支援卡属性和效果 |
| character*.br, uma*.br, chara*.br | 角色数据 | 马娘角色信息 |
| names.br | 名称数据 | 各种名称映射 |
| skill*.br | 技能数据 | 技能效果和描述 |
| race*.br | 比赛数据 | 比赛信息和奖励 |

## 🎯 使用示例

```cmd
# 解压事件数据
python br_extractor.py "../events/events.br"
# 输出: events_extracted.json

# 解压角色名称数据
python br_extractor.py "../events/names.br" "character_names.json"
# 输出: character_names.json
```

## 🛠️ 故障排除

**Q: 提示Python未找到**
A: 安装Python 3.8+并添加到PATH

**Q: 权限错误**
A: 以管理员身份运行或检查输出目录权限

**Q: 解压失败**
A: 检查文件是否完整，或尝试不同的解压方法

## 📈 输出示例

```json
[
  {
    "Id": 100001,
    "Name": "イベント名",
    "TriggerName": "URA",
    "Choices": [
      {
        "Option": "選択肢",
        "SuccessEffect": "効果説明",
        "SuccessEffectValue": {
          "Values": [10, 0, 0, 0, 0, 20, ...]
        }
      }
    ]
  }
]
```

---

**版本**: v2.0  
**更新**: 2025-01-22  
**作者**: URA AI Project Team
