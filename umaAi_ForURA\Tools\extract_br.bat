@echo off
echo Data Extraction Tool Launcher
echo ===============================

REM Change to script directory
cd /d "%~dp0"

REM Check if Python is installed
echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    echo Please install Python 3.8+ and add to PATH
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo Python found!

REM Check if brotli library is installed
echo Checking brotli library...
python -c "import brotli" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing brotli library...
    pip install brotli
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install brotli
        echo Please run: pip install brotli
        echo.
        echo Press any key to exit...
        pause >nul
        exit /b 1
    )
    echo Brotli library installed successfully!
)

echo Environment check completed!
echo.

REM Check for command line arguments
if "%1"=="" (
    echo Starting interactive mode...
    python simple_br_extractor.py
) else (
    echo Starting command line mode...
    if "%2"=="" (
        python simple_br_extractor.py "%1"
    ) else (
        python simple_br_extractor.py "%1" "%2"
    )
)

echo.
echo Operation completed!
echo Press any key to exit...
pause >nul
