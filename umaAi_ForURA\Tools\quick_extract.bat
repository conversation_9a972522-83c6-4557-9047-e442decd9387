@echo off
echo Quick BR File Extractor
echo ======================

REM Change to script directory
cd /d "%~dp0"

REM Check if file is provided
if "%1"=="" (
    echo Usage: Drag a .br file onto this script
    echo Or run: quick_extract.bat "path\to\file.br"
    pause
    exit /b 1
)

REM Get input file
set INPUT_FILE=%1
set INPUT_FILE=%INPUT_FILE:"=%

REM Check if input file exists
if not exist "%INPUT_FILE%" (
    echo ERROR: File not found: %INPUT_FILE%
    pause
    exit /b 1
)

REM Generate output filename
for %%F in ("%INPUT_FILE%") do (
    set BASE_NAME=%%~nF
)
set OUTPUT_FILE=%BASE_NAME%_extracted.json

echo Input file: %INPUT_FILE%
echo Output file: %OUTPUT_FILE%
echo.

REM Run extraction
echo Starting extraction...
python simple_br_extractor.py "%INPUT_FILE%" "%OUTPUT_FILE%"

if %errorlevel% equ 0 (
    echo.
    echo SUCCESS: Extraction completed!
    echo Output saved to: %OUTPUT_FILE%
    
    REM Show file size
    for %%A in ("%OUTPUT_FILE%") do (
        echo File size: %%~zA bytes
    )
) else (
    echo.
    echo ERROR: Extraction failed!
)

echo.
pause
