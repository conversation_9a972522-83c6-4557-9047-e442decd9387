# URA剧本模拟器设计框架

## 📋 项目概述

**目标**: 基于UmaAI设计思路，开发高精度的URA剧本模拟器  
**参考**: UmaAI-UAF项目的架构设计  
**特点**: 专门针对URA剧本优化，简化复杂机制，提高模拟精度  

## 🏗️ 整体架构设计

### **分层架构**
```
┌─────────────────────────────────────┐
│           AI决策层                   │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │ 神经网络AI  │ │ 蒙特卡洛搜索    │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│           游戏逻辑层                 │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │ 事件系统    │ │ 训练系统        │ │
│  │ 比赛系统    │ │ 支援卡系统      │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│           数据层                     │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │ 游戏状态    │ │ 配置数据        │ │
│  │ 历史记录    │ │ 事件数据        │ │
│  └─────────────┘ └─────────────────┘ │
└─────────────────────────────────────┘
```

## 🎮 核心系统设计

### **1. 游戏状态管理 (GameState)**

#### **基础状态**
```cpp
struct URAGameState {
    // 基础信息
    int turn;                    // 当前回合 (0-77)
    int character_id;            // 角色ID
    
    // 五维属性
    int speed, stamina, power, guts, wisdom;
    
    // 体力和干劲
    int vital;                   // 体力 (0-100)
    int motivation;              // 干劲 (1-5)
    
    // 技能和粉丝
    int skill_points;            // 技能点
    int fan_count;               // 粉丝数
    
    // 支援卡状态
    vector<SupportCard> support_cards;
    vector<int> friendship_levels;  // 友情度
    
    // 比赛记录
    vector<RaceResult> race_history;
    int race_wins;               // 比赛胜利数
    
    // 事件历史
    vector<int> triggered_events;
    
    // 计算总评分
    int calculateTotalScore() const;
    
    // 检查游戏结束
    bool isGameEnd() const { return turn >= 78; }
};
```

#### **状态转换**
```cpp
class StateTransition {
public:
    // 执行动作并更新状态
    bool executeAction(URAGameState& state, const Action& action);
    
    // 处理回合结束事件
    void processEndOfTurn(URAGameState& state);
    
    // 处理比赛
    RaceResult processRace(URAGameState& state, int race_id);
    
private:
    EventSystem* event_system;
    TrainingSystem* training_system;
};
```

### **2. 动作系统 (Action System)**

#### **动作类型定义**
```cpp
enum ActionType {
    // 基础训练
    TRAIN_SPEED = 0,
    TRAIN_STAMINA = 1,
    TRAIN_POWER = 2,
    TRAIN_GUTS = 3,
    TRAIN_WISDOM = 4,
    
    // 其他动作
    REST = 5,                    // 休息
    OUTGOING = 6,                // 外出
    INFIRMARY = 7,               // 保健室
    
    // 比赛
    RACE = 8,                    // 参加比赛
    
    // 事件选择
    EVENT_CHOICE_1 = 100,        // 事件选择1
    EVENT_CHOICE_2 = 101,        // 事件选择2
    EVENT_CHOICE_3 = 102         // 事件选择3
};

struct Action {
    ActionType type;
    int target_id;               // 目标ID（如支援卡ID）
    int choice_index;            // 选择索引（事件用）
    
    Action(ActionType t, int target = -1, int choice = -1) 
        : type(t), target_id(target), choice_index(choice) {}
};
```

#### **动作生成器**
```cpp
class ActionGenerator {
public:
    // 获取当前可用动作
    vector<Action> getAvailableActions(const URAGameState& state);
    
    // 检查动作是否有效
    bool isActionValid(const URAGameState& state, const Action& action);
    
private:
    // 检查训练动作
    bool canTrain(const URAGameState& state, ActionType training_type);
    
    // 检查比赛动作
    bool canRace(const URAGameState& state);
};
```

### **3. 训练系统 (Training System)**

#### **训练计算**
```cpp
struct TrainingResult {
    int status_gains[5];         // 五维属性获得
    int vital_cost;              // 体力消耗
    int skill_points_gained;     // 技能点获得
    bool is_success;             // 是否成功
    bool is_great_success;       // 是否大成功
    vector<int> friendship_gains; // 友情度获得
};

class TrainingSystem {
public:
    // 计算训练结果
    TrainingResult calculateTraining(
        const URAGameState& state, 
        ActionType training_type
    );
    
private:
    // 基础训练收益
    int getBaseTrainingGain(ActionType type, int motivation);
    
    // 支援卡加成
    int getSupportCardBonus(const URAGameState& state, ActionType type);
    
    // 友情训练加成
    int getFriendshipBonus(const URAGameState& state, ActionType type);
    
    // 计算成功率
    double getSuccessRate(const URAGameState& state, ActionType type);
};
```

### **4. 事件系统 (Event System)**

#### **基于真实数据的事件处理**
```cpp
struct EventChoice {
    string option_text;          // 选择文本
    EffectValue success_effect;  // 成功效果（基于UmamusumeDeserializeDB5）
    EffectValue failed_effect;   // 失败效果
    int success_rate;            // 成功率
};

struct GameEvent {
    int event_id;
    string name;
    string trigger_name;         // "URA" 或角色名
    vector<EventChoice> choices;
    
    // 检查触发条件
    bool canTrigger(const URAGameState& state) const;
};

class EventSystem {
public:
    // 从events.br数据加载事件
    bool loadEventsFromDatabase(const string& events_file);
    
    // 检查事件触发
    const GameEvent* checkEventTrigger(const URAGameState& state);
    
    // 应用事件效果
    void applyEventEffect(URAGameState& state, const EffectValue& effect);
    
private:
    vector<GameEvent> ura_events;    // URA剧本事件
    EventTrigger trigger_manager;    // 触发管理器
};
```

### **5. 比赛系统 (Race System)**

#### **URA剧本比赛安排**
```cpp
struct RaceSchedule {
    int turn;                    // 比赛回合
    string race_name;            // 比赛名称
    int required_fans;           // 粉丝数要求
    bool is_mandatory;           // 是否必须参加
};

// URA剧本固定比赛
const vector<RaceSchedule> URA_RACES = {
    {13, "新马战", 0, true},
    {24, "未胜利战", 1000, true},
    {37, "500万下", 3000, true},
    {48, "1000万下", 7500, true},
    {61, "1600万下", 15000, true},
    {72, "OP", 30000, true},
    {75, "G3", 45000, true},
    {76, "G2", 60000, true},
    {77, "G1", 75000, true}
};

class RaceSystem {
public:
    // 计算比赛结果
    RaceResult calculateRaceResult(const URAGameState& state, int race_id);
    
    // 获取当前可参加的比赛
    vector<int> getAvailableRaces(const URAGameState& state);
    
private:
    // 计算比赛实力
    int calculateRacePower(const URAGameState& state, int race_id);
    
    // 计算胜率
    double calculateWinRate(int race_power, int race_difficulty);
};
```

## 🤖 AI决策系统设计

### **1. 多层决策架构**

#### **规则AI (基础层)**
```cpp
class RuleBasedAI {
public:
    Action selectAction(const URAGameState& state);
    
private:
    // 体力管理策略
    bool needRest(const URAGameState& state);
    
    // 属性平衡策略
    ActionType selectTrainingType(const URAGameState& state);
    
    // 比赛参加策略
    bool shouldParticipateRace(const URAGameState& state, int race_id);
};
```

#### **神经网络AI (高级层)**
```cpp
class NeuralNetworkAI {
public:
    // 状态评估
    double evaluateState(const URAGameState& state);
    
    // 动作价值评估
    vector<double> evaluateActions(
        const URAGameState& state, 
        const vector<Action>& actions
    );
    
    // 选择最佳动作
    Action selectBestAction(const URAGameState& state);
    
private:
    // 状态特征提取
    vector<double> extractFeatures(const URAGameState& state);
    
    // 神经网络推理
    vector<double> networkInference(const vector<double>& features);
};
```

#### **蒙特卡洛树搜索 (MCTS)**
```cpp
class MCTSNode {
public:
    URAGameState state;
    Action action;
    double value_sum;
    int visit_count;
    vector<unique_ptr<MCTSNode>> children;
    
    // UCB1选择
    double getUCB1Value(double exploration_param) const;
};

class MCTS {
public:
    Action search(const URAGameState& root_state, int iterations);
    
private:
    // 选择阶段
    MCTSNode* select(MCTSNode* node);
    
    // 扩展阶段
    void expand(MCTSNode* node);
    
    // 模拟阶段
    double simulate(const URAGameState& state);
    
    // 回传阶段
    void backpropagate(MCTSNode* node, double value);
};
```

### **2. 训练数据生成**

#### **自对弈系统**
```cpp
class SelfPlayGenerator {
public:
    // 生成训练数据
    vector<TrainingExample> generateTrainingData(int num_games);
    
    // 单局自对弈
    GameResult playSelfGame();
    
private:
    NeuralNetworkAI* current_ai;
    MCTS* search_engine;
    
    // 记录训练样本
    void recordTrainingExample(
        const URAGameState& state,
        const vector<double>& action_probs,
        double final_score
    );
};
```

## 📊 性能优化设计

### **1. 状态表示优化**
```cpp
// 紧凑的状态表示
struct CompactGameState {
    uint32_t turn : 7;           // 0-77
    uint32_t vital : 7;          // 0-100
    uint32_t motivation : 3;     // 1-5
    uint32_t speed : 12;         // 0-4095
    uint32_t stamina : 12;       // 0-4095
    // ... 其他字段
    
    // 转换函数
    URAGameState toFullState() const;
    static CompactGameState fromFullState(const URAGameState& state);
};
```

### **2. 缓存系统**
```cpp
class StateCache {
public:
    // 缓存状态评估
    void cacheEvaluation(const URAGameState& state, double value);
    
    // 获取缓存评估
    optional<double> getCachedEvaluation(const URAGameState& state);
    
private:
    unordered_map<size_t, double> evaluation_cache;
    
    // 状态哈希
    size_t hashState(const URAGameState& state);
};
```

## 🔧 配置和数据管理

### **1. 配置系统**
```cpp
struct URAConfig {
    // 训练参数
    double base_training_gains[5];
    double motivation_multipliers[5];
    double friendship_bonus_rate;
    
    // 比赛参数
    double race_difficulty_factors[9];
    int fan_gain_rates[9];
    
    // AI参数
    double mcts_exploration_param;
    int mcts_iterations;
    double neural_network_temperature;
    
    // 从JSON加载
    static URAConfig loadFromFile(const string& config_file);
};
```

### **2. 数据管理**
```cpp
class DataManager {
public:
    // 加载游戏数据
    bool loadGameData(const string& data_directory);
    
    // 获取事件数据
    const vector<GameEvent>& getEvents() const { return events; }
    
    // 获取支援卡数据
    const vector<SupportCard>& getSupportCards() const { return support_cards; }
    
private:
    vector<GameEvent> events;
    vector<SupportCard> support_cards;
    URAConfig config;
};
```

## 🎯 实现优先级

### **Phase 1: 核心框架 (2周)**
1. ✅ **基础状态管理** - URAGameState和StateTransition
2. ✅ **动作系统** - Action定义和ActionGenerator
3. ✅ **简单训练系统** - 基础训练计算
4. ✅ **规则AI** - 基础决策逻辑

### **Phase 2: 数据集成 (2周)**
1. 🔄 **事件系统** - 基于events.br的真实事件
2. 🔄 **比赛系统** - URA剧本比赛流程
3. 🔄 **支援卡系统** - 基础支援卡效果
4. 🔄 **配置系统** - 参数调优

### **Phase 3: AI优化 (4周)**
1. 📋 **神经网络AI** - 深度学习模型
2. 📋 **MCTS搜索** - 蒙特卡洛树搜索
3. 📋 **自对弈训练** - 训练数据生成
4. 📋 **性能优化** - 速度和内存优化

### **Phase 4: 完善和发布 (2周)**
1. 📋 **用户界面** - 可视化界面
2. 📋 **测试和调试** - 全面测试
3. 📋 **文档完善** - 使用说明
4. 📋 **性能评估** - 与真实游戏对比

## 💡 关键设计原则

### **1. 模块化设计**
- 每个系统独立开发和测试
- 清晰的接口定义
- 易于扩展和维护

### **2. 数据驱动**
- 基于真实游戏数据
- 配置文件驱动参数
- 支持热更新

### **3. 性能优先**
- 高效的状态表示
- 智能缓存策略
- 并行计算支持

### **4. 可扩展性**
- 支持其他剧本扩展
- 插件化AI系统
- 模块化数据格式

---

**设计版本**: v1.0  
**创建时间**: 2025年1月22日  
**基于**: UmaAI设计思路 + URA剧本特点  
**目标**: 高精度URA剧本模拟器
