// 练习1.1：游戏状态初始化
// 目标：掌握变量声明和初始化

#include <iostream>
#include <string>

int main() {
    std::cout << "=== 练习1.1：游戏状态初始化 ===" << std::endl;
    
    // TODO: 在这里完成练习
    // 1. 声明5个整数变量表示五维属性，初始值都设为100
    // 2. 声明体力变量，初始值为100
    // 3. 声明回合数变量，初始值为0
    // 4. 输出所有变量的值
    
    // 示例代码（请删除这些注释，写出自己的实现）：
    /*
    int speed = 100;
    int stamina = 100;
    // ... 继续完成其他变量
    
    std::cout << "速度: " << speed << std::endl;
    // ... 继续输出其他变量
    */
    
    std::cout << "练习完成！按回车键退出..." << std::endl;
    std::cin.get();
    return 0;
}
