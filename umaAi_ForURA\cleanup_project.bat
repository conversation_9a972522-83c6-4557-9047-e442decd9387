@echo off
echo Project Cleanup Tool
echo ===================

echo This script will help clean up the project structure.
echo.
echo What would you like to clean?
echo 1. Remove duplicate files in Tools/
echo 2. Clean bin/ directory (keep .exe files)
echo 3. Remove temporary files
echo 4. Show file sizes and recommendations
echo 5. Exit

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto :clean_tools
if "%choice%"=="2" goto :clean_bin
if "%choice%"=="3" goto :clean_temp
if "%choice%"=="4" goto :show_info
if "%choice%"=="5" goto :exit
goto :invalid

:clean_tools
echo.
echo Cleaning Tools directory...
echo.
echo Files that can be removed (duplicates of DataExtractor):
if exist "Tools\simple_br_extractor.py" echo - Tools\simple_br_extractor.py (use DataExtractor\br_extractor.py instead)
if exist "Tools\extract_br.bat" echo - Tools\extract_br.bat (use DataExtractor\extract.bat instead)
if exist "Tools\extract_br_data.py" echo - Tools\extract_br_data.py (old version)
if exist "Tools\install_dependencies.bat" echo - Tools\install_dependencies.bat (functionality moved)
if exist "Tools\数据提取工具使用说明.md" echo - Tools\数据提取工具使用说明.md (use DataExtractor\README.md instead)

echo.
set /p confirm="Remove these duplicate files? (y/n): "
if /i "%confirm%"=="y" (
    if exist "Tools\simple_br_extractor.py" del "Tools\simple_br_extractor.py"
    if exist "Tools\extract_br.bat" del "Tools\extract_br.bat"
    if exist "Tools\extract_br_data.py" del "Tools\extract_br_data.py"
    if exist "Tools\install_dependencies.bat" del "Tools\install_dependencies.bat"
    if exist "Tools\数据提取工具使用说明.md" del "Tools\数据提取工具使用说明.md"
    echo ✅ Duplicate files removed!
) else (
    echo Cleanup cancelled.
)
goto :menu

:clean_bin
echo.
echo Cleaning bin directory...
echo.
echo Files that can be removed:
if exist "bin\*.obj" echo - Object files (*.obj)
if exist "bin\*.pdb" echo - Debug files (*.pdb)
if exist "bin\*.ilk" echo - Incremental link files (*.ilk)

echo.
echo Files that will be kept:
if exist "bin\*.exe" echo - Executable files (*.exe)

echo.
set /p confirm="Remove temporary build files? (y/n): "
if /i "%confirm%"=="y" (
    if exist "bin\*.obj" del "bin\*.obj"
    if exist "bin\*.pdb" del "bin\*.pdb"
    if exist "bin\*.ilk" del "bin\*.ilk"
    echo ✅ Build files cleaned!
) else (
    echo Cleanup cancelled.
)
goto :menu

:clean_temp
echo.
echo Cleaning temporary files...
echo.
if exist "Tools\test_report.json" del "Tools\test_report.json"
if exist "Tools\*_extracted.json" del "Tools\*_extracted.json"
if exist "*.tmp" del "*.tmp"
if exist "*.log" del "*.log"
echo ✅ Temporary files cleaned!
goto :menu

:show_info
echo.
echo Project Structure Analysis:
echo ==========================
echo.

echo Directory sizes:
for /d %%d in (*) do (
    if exist "%%d" (
        echo %%d\
    )
)

echo.
echo Large files (>1MB):
forfiles /s /m *.* /c "cmd /c if @fsize gtr 1048576 echo @path - @fsize bytes" 2>nul

echo.
echo Recommendations:
echo - Use DataExtractor\ for all data extraction tasks
echo - Keep only necessary files in Tools\
echo - Regularly clean bin\ directory
echo - Archive old data files if not needed

goto :menu

:invalid
echo Invalid choice. Please enter 1-5.

:menu
echo.
echo Press any key to return to menu...
pause >nul
cls
goto :start

:start
echo Project Cleanup Tool
echo ===================

echo This script will help clean up the project structure.
echo.
echo What would you like to clean?
echo 1. Remove duplicate files in Tools/
echo 2. Clean bin/ directory (keep .exe files)
echo 3. Remove temporary files
echo 4. Show file sizes and recommendations
echo 5. Exit

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto :clean_tools
if "%choice%"=="2" goto :clean_bin
if "%choice%"=="3" goto :clean_temp
if "%choice%"=="4" goto :show_info
if "%choice%"=="5" goto :exit
goto :invalid

:exit
echo.
echo Cleanup completed!
pause
