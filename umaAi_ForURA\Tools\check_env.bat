@echo off
echo Environment Check Tool
echo ======================

echo 1. Checking current directory...
echo Current directory: %CD%
echo Script location: %~dp0

echo.
echo 2. Checking Python installation...
python --version 2>nul
if %errorlevel% equ 0 (
    echo Python: OK
) else (
    echo Python: NOT FOUND
    echo Trying 'py' command...
    py --version 2>nul
    if %errorlevel% equ 0 (
        echo Python (py): OK
    ) else (
        echo Python (py): NOT FOUND
        echo ERROR: Python is not installed or not in PATH
    )
)

echo.
echo 3. Checking pip...
pip --version 2>nul
if %errorlevel% equ 0 (
    echo Pip: OK
) else (
    echo Pip: NOT FOUND
)

echo.
echo 4. Checking files...
if exist "simple_br_extractor.py" (
    echo simple_br_extractor.py: OK
) else (
    echo simple_br_extractor.py: NOT FOUND
)

if exist "..\events\events.br" (
    echo events.br: OK
) else (
    echo events.br: NOT FOUND
)

echo.
echo 5. Testing Python execution...
python -c "print('Python test: OK')" 2>nul
if %errorlevel% neq 0 (
    py -c "print('Python test: OK')" 2>nul
    if %errorlevel% neq 0 (
        echo Python execution: FAILED
    )
)

echo.
echo Check completed!
pause
