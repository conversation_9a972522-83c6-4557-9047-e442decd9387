# C++练习题答案

## 📝 使用说明

这个文件包含所有练习题的参考答案。建议您：
1. **先独立完成练习** - 不要直接看答案
2. **对比检查** - 完成后对比答案找出差异
3. **理解思路** - 重点理解解题思路，而不是死记代码

---

## 练习1.1：游戏状态初始化

```cpp
#include <iostream>
#include <string>

int main() {
    std::cout << "=== 练习1.1：游戏状态初始化 ===" << std::endl;
    
    // 声明五维属性变量
    int speed = 100;
    int stamina = 100;
    int power = 100;
    int guts = 100;
    int wisdom = 100;
    
    // 声明其他游戏状态变量
    int vital = 100;
    int turn = 0;
    
    // 输出所有变量的值
    std::cout << "速度: " << speed << std::endl;
    std::cout << "耐力: " << stamina << std::endl;
    std::cout << "力量: " << power << std::endl;
    std::cout << "根性: " << guts << std::endl;
    std::cout << "智慧: " << wisdom << std::endl;
    std::cout << "体力: " << vital << std::endl;
    std::cout << "回合: " << turn << std::endl;
    
    std::cout << "练习完成！按回车键退出..." << std::endl;
    std::cin.get();
    return 0;
}
```

**学习要点**：
- 变量声明和初始化语法
- std::cout的使用
- std::endl的作用

---

## 练习1.2：条件判断

```cpp
#include <iostream>
#include <string>

std::string getRecommendedAction(int vital) {
    if (vital < 30) {
        return "休息";
    } else if (vital >= 30 && vital <= 70) {
        return "外出";
    } else {  // vital > 70
        return "训练";
    }
}

int main() {
    std::cout << "=== 练习1.2：条件判断 ===" << std::endl;
    
    // 测试不同的体力值
    int test_vitals[] = {20, 50, 80, 100};
    int test_count = sizeof(test_vitals) / sizeof(test_vitals[0]);
    
    for (int i = 0; i < test_count; i++) {
        int vital = test_vitals[i];
        std::string action = getRecommendedAction(vital);
        std::cout << "体力 " << vital << " -> 推荐动作: " << action << std::endl;
    }
    
    std::cout << "练习完成！按回车键退出..." << std::endl;
    std::cin.get();
    return 0;
}
```

**学习要点**：
- if-else语句的使用
- 函数定义和调用
- 数组和循环的基本使用

---

## 练习2.1：创建马娘结构体

```cpp
#include <iostream>
#include <string>

struct Uma {
    std::string name;
    int speed, stamina, power, guts, wisdom;
    int vital;
    int turn;
    
    // 构造函数
    Uma() {
        name = "默认马娘";
        speed = stamina = power = guts = wisdom = 100;
        vital = 100;
        turn = 0;
    }
    
    // 带参数的构造函数
    Uma(const std::string& n) {
        name = n;
        speed = stamina = power = guts = wisdom = 100;
        vital = 100;
        turn = 0;
    }
    
    // 获取五维属性总和
    int getTotalStatus() const {
        return speed + stamina + power + guts + wisdom;
    }
    
    // 判断体力是否低于30
    bool isLowVital() const {
        return vital < 30;
    }
    
    // 显示马娘状态
    void displayStatus() const {
        std::cout << "=== " << name << " 的状态 ===" << std::endl;
        std::cout << "速度: " << speed << std::endl;
        std::cout << "耐力: " << stamina << std::endl;
        std::cout << "力量: " << power << std::endl;
        std::cout << "根性: " << guts << std::endl;
        std::cout << "智慧: " << wisdom << std::endl;
        std::cout << "体力: " << vital << std::endl;
        std::cout << "回合: " << turn << std::endl;
        std::cout << "总评分: " << getTotalStatus() << std::endl;
        std::cout << "体力状态: " << (isLowVital() ? "低" : "正常") << std::endl;
    }
};

int main() {
    std::cout << "=== 练习2.1：创建马娘结构体 ===" << std::endl;
    
    // 创建马娘实例
    Uma uma1;  // 使用默认构造函数
    Uma uma2("特别周");  // 使用带参数构造函数
    
    // 修改一些属性
    uma2.speed = 150;
    uma2.vital = 25;  // 设置为低体力
    uma2.turn = 10;
    
    // 显示状态
    uma1.displayStatus();
    std::cout << std::endl;
    uma2.displayStatus();
    
    std::cout << "练习完成！按回车键退出..." << std::endl;
    std::cin.get();
    return 0;
}
```

**学习要点**：
- 结构体的定义和使用
- 构造函数的重载
- const成员函数
- 成员函数的实现

---

## 练习3.1：技能管理

```cpp
#include <iostream>
#include <vector>
#include <algorithm>

class SkillManager {
private:
    std::vector<int> skills;
    
public:
    // 添加技能
    void addSkill(int skill_id) {
        // 检查是否已经存在
        if (hasSkill(skill_id)) {
            std::cout << "技能 " << skill_id << " 已经存在！" << std::endl;
            return;
        }
        
        skills.push_back(skill_id);
        std::cout << "成功添加技能 " << skill_id << std::endl;
    }
    
    // 查找技能
    bool hasSkill(int skill_id) const {
        return std::find(skills.begin(), skills.end(), skill_id) != skills.end();
    }
    
    // 显示所有技能
    void displayAllSkills() const {
        if (skills.empty()) {
            std::cout << "没有学会任何技能" << std::endl;
            return;
        }
        
        std::cout << "已学技能: ";
        for (size_t i = 0; i < skills.size(); i++) {
            std::cout << skills[i];
            if (i < skills.size() - 1) {
                std::cout << ", ";
            }
        }
        std::cout << std::endl;
    }
    
    // 获取技能数量
    int getSkillCount() const {
        return static_cast<int>(skills.size());
    }
    
    // 移除技能
    void removeSkill(int skill_id) {
        auto it = std::find(skills.begin(), skills.end(), skill_id);
        if (it != skills.end()) {
            skills.erase(it);
            std::cout << "成功移除技能 " << skill_id << std::endl;
        } else {
            std::cout << "技能 " << skill_id << " 不存在！" << std::endl;
        }
    }
};

int main() {
    std::cout << "=== 练习3.1：技能管理 ===" << std::endl;
    
    SkillManager manager;
    
    // 测试添加技能
    manager.addSkill(101);
    manager.addSkill(102);
    manager.addSkill(103);
    manager.addSkill(101);  // 重复添加
    
    // 显示所有技能
    manager.displayAllSkills();
    std::cout << "技能数量: " << manager.getSkillCount() << std::endl;
    
    // 测试查找技能
    std::cout << "是否有技能102: " << (manager.hasSkill(102) ? "是" : "否") << std::endl;
    std::cout << "是否有技能999: " << (manager.hasSkill(999) ? "是" : "否") << std::endl;
    
    // 测试移除技能
    manager.removeSkill(102);
    manager.displayAllSkills();
    
    std::cout << "练习完成！按回车键退出..." << std::endl;
    std::cin.get();
    return 0;
}
```

**学习要点**：
- std::vector的使用
- std::find算法的使用
- 类的封装和方法设计
- 错误处理和边界检查

---

## 练习4.1：动作枚举

```cpp
#include <iostream>
#include <string>

enum TrainingType {
    SPEED = 0,
    STAMINA = 1,
    POWER = 2,
    GUTS = 3,
    WISDOM = 4,
    REST = 5,
    OUTGOING = 6
};

std::string getActionName(TrainingType type) {
    switch (type) {
        case SPEED: return "速度训练";
        case STAMINA: return "耐力训练";
        case POWER: return "力量训练";
        case GUTS: return "根性训练";
        case WISDOM: return "智慧训练";
        case REST: return "休息";
        case OUTGOING: return "外出";
        default: return "未知动作";
    }
}

bool isTraining(TrainingType type) {
    return type >= SPEED && type <= WISDOM;
}

int main() {
    std::cout << "=== 练习4.1：动作枚举 ===" << std::endl;
    
    // 测试所有动作类型
    TrainingType actions[] = {SPEED, STAMINA, POWER, GUTS, WISDOM, REST, OUTGOING};
    int action_count = sizeof(actions) / sizeof(actions[0]);
    
    for (int i = 0; i < action_count; i++) {
        TrainingType action = actions[i];
        std::cout << "动作 " << static_cast<int>(action) << ": " 
                  << getActionName(action) 
                  << " (是否为训练: " << (isTraining(action) ? "是" : "否") << ")"
                  << std::endl;
    }
    
    std::cout << "练习完成！按回车键退出..." << std::endl;
    std::cin.get();
    return 0;
}
```

**学习要点**：
- 枚举的定义和使用
- switch语句
- 类型转换 (static_cast)
- 函数的设计和实现

---

## 练习5.1：训练成功判定

```cpp
#include <iostream>
#include <random>
#include <iomanip>

class TrainingSimulator {
private:
    std::mt19937 rng;
    
public:
    TrainingSimulator() : rng(std::time(nullptr)) {}
    
    bool isTrainingSuccess(int success_rate) {
        if (success_rate < 0) success_rate = 0;
        if (success_rate > 100) success_rate = 100;
        
        int random_value = rng() % 100;
        return random_value < success_rate;
    }
    
    double testSuccessRate(int success_rate, int test_count) {
        int success_count = 0;
        
        for (int i = 0; i < test_count; i++) {
            if (isTrainingSuccess(success_rate)) {
                success_count++;
            }
        }
        
        return static_cast<double>(success_count) / test_count * 100.0;
    }
};

int main() {
    std::cout << "=== 练习5.1：训练成功判定 ===" << std::endl;
    
    TrainingSimulator simulator;
    
    // 测试不同成功率
    int test_rates[] = {20, 50, 80};
    int test_count = 1000;
    
    std::cout << std::fixed << std::setprecision(1);
    
    for (int rate : test_rates) {
        double actual_rate = simulator.testSuccessRate(rate, test_count);
        std::cout << "设定成功率: " << rate << "%, "
                  << "实际成功率: " << actual_rate << "% "
                  << "(测试" << test_count << "次)" << std::endl;
    }
    
    // 单次测试示例
    std::cout << "\n单次测试示例:" << std::endl;
    for (int i = 0; i < 10; i++) {
        bool success = simulator.isTrainingSuccess(70);
        std::cout << "第" << (i+1) << "次训练: " << (success ? "成功" : "失败") << std::endl;
    }
    
    std::cout << "练习完成！按回车键退出..." << std::endl;
    std::cin.get();
    return 0;
}
```

**学习要点**：
- std::mt19937随机数生成器
- 概率计算和统计
- 浮点数格式化输出
- 类的构造函数初始化列表

---

## 🎯 学习建议

### 完成练习后的检查清单
- [ ] 代码能够成功编译
- [ ] 运行结果符合预期
- [ ] 理解每行代码的作用
- [ ] 能够解释使用的C++概念
- [ ] 尝试修改代码验证理解

### 进阶练习建议
1. **优化代码** - 让代码更简洁高效
2. **添加功能** - 扩展现有功能
3. **错误处理** - 添加更完善的错误检查
4. **代码复用** - 将通用功能提取为函数

### 遇到困难时
1. 仔细阅读编译错误信息
2. 对照教学文档查找相关语法
3. 使用cout输出中间结果调试
4. 从简单的部分开始，逐步完善

**继续加油！每完成一个练习，您就离掌握C++更近一步！** 🚀
