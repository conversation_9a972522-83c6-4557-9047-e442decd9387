@echo off
chcp 65001 >nul
echo 🏇 赛马娘数据提取工具启动器
echo ================================

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Python
    echo 请安装Python 3.8+
    pause
    exit /b 1
)

REM 检查brotli库是否安装
python -c "import brotli" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 正在安装brotli库...
    pip install brotli
    if %errorlevel% neq 0 (
        echo ❌ 安装brotli失败
        pause
        exit /b 1
    )
    echo ✅ brotli库安装成功
)

echo ✅ 环境检查完成
echo.

REM 检查是否有命令行参数
if "%1"=="" (
    echo 🚀 启动交互模式...
    python simple_br_extractor.py
) else (
    echo 🚀 启动命令行模式...
    if "%2"=="" (
        python simple_br_extractor.py "%1"
    ) else (
        python simple_br_extractor.py "%1" "%2"
    )
)

echo.
echo 操作完成！
pause
